from _io_utils import read_inferred_data
import pandas as pd
import plotly.graph_objects as go
import plotly.offline as py_offline
from plotly.subplots import make_subplots

options = {
    'pathnames': ['outputs/plateo06.3 CT Tests/Acinotech Forceplate 5 Lab Run Heel 2 R->L 20250424_152226.csv'], 
    'plates': ['Acinotech Forceplate 5'],
    'analysis_threshold': 50, # Fz threshold to be included in accuracy analysis
    'daq_version': 1, # ["STM32", "Pi", 0, 1] 0 for ESP32, 1 for EtherCAT
    'save': False, # save to outputs folder
    'plate_sizes': [(500,500)], # x,y
    'plate_centers': [(0, 0)], # x,y, relative to ground truth COP origin
}

if not options['daq_version'] in ["STM32", "Pi", 0, 1]:
    raise ValueError("Invalid DAQ version specified - must be one of ['STM32', 'Pi', 0, 1]")

if __name__ == '__main__':
    output_names = ['Fx', 'Fy', 'Fz', 'Cx', 'Cy']
    units = ['N', 'N', 'N', 'mm', 'mm']
    forces = ['Fx', 'Fy', 'Fz']
    CoPs = ['Cx', 'Cy']
    whole_set = pd.DataFrame()
    plate_name = options['pathnames'][0].split('/')[-1]

    # Load the data
    data = read_inferred_data(options['pathnames'], options['plates'], skip_rows=0, usecols=output_names, names=output_names)

    # Process the data
    for (i, (trialname, plates)) in enumerate(data):
        py_offline.init_notebook_mode(connected=True)
        
        # Create a figure for the trial with rows for each plate's outputs
        total_rows = len(plates) * (len(output_names) + 1) - 1  # Extra row for spacing between plate sets
        fig = make_subplots(rows=total_rows, cols=1, shared_xaxes=True, 
                            vertical_spacing=0.02)  # Reduce spacing slightly, we'll manually space

        row_offset = 0  # Used to position each plate's plots

        for j, (plate, df) in enumerate(plates):
            fullpath = trialname + "/" + plate
            print(f"Processing file {i+1}/{len(data)}: " + fullpath)

            # Add a header for each plate
            fig.add_annotation(
                text=f"Plate: {plate}",
                xref="paper", yref="paper",
                x=0.5, y=1 - (row_offset / total_rows),
                xanchor="center", yanchor="bottom",
                showarrow=False,
                font=dict(size=16, color="black"),
                align="center",
                borderwidth=1,
            )

            linecolor = 'rgba(0,0,50,0.8)'

            # Add traces for each output component with custom hover information including CoP values
            for k, output in enumerate(output_names):
                tooltip_text = df.apply(
                    lambda row: '<br>'.join([f"{plate} - {f}: {row[f]:.2f}" for f in forces + CoPs]), axis=1)
                fig.add_trace(go.Scatter(x=df.index-df.index[0], y=df[output], mode='lines', 
                                         name=f"{plate} - {output}", hoverinfo='text', text=tooltip_text, line=dict(color=linecolor)),
                              row=row_offset + k + 1, col=1)

                # Set Y-axis labels for each subplot
                fig.update_yaxes(title_text=output, row=row_offset + k + 1, col=1)

            # Move to the next set of plots for the next plate, adding an extra row for spacing
            row_offset += len(output_names) + 1  # Extra +1 for the gap between plates

        # Update layout with title and axis labels
        fig.update_layout(title=f"Trial {i+1}: {trialname}",
                          height=100 * total_rows,  # Adjust height per subplot including gaps
                          showlegend=False)
        
        fig.update_xaxes(title_text='Time (s)', row=total_rows)
        
        py_offline.plot(fig)