import numpy as np
import scipy.signal
import os
import pandas as pd

def read_data(pathname, output_type='DataFrame', daq_type=0, skip_rows=10) -> list:
    """
    Read in the data from the path and return a list of tuples of the file name(s) and the data
    
    Parameters
    ----------
    pathname : str | list
        The path to the file or folder to read in
    output_type : str
        The type of data to return. Options are 'DataFrame' or 'numpy.ndarray'
    daq_type : int | str
        The type of DAQ used to capture the data. Options are 'Pi', 0, 1
    skip_rows : int
        The number of rows to skip when reading in the data
            
    Returns
    -------
    data : list of tuples
        A list of tuples of the file name and the data
    """
    data = []
    files = []

    if type(pathname) != list:
        pathname = [pathname]
    
    for item in pathname:
        # if path is a folder, read in all the files in the folder
        if os.path.isdir(item):
            for root, dirs, children in os.walk(item): # recursive
                for file in children: #append the file name to the list
                    files.append(os.path.join(root,file))
            
            # files = [os.path.join(item, f) for f in os.listdir(item) if os.path.isfile(os.path.join(item, f))]
        # otherwise, read in the file
        else:
            files = [item]
    
        for f in files:
            if f.endswith('.csv'):
                if output_type == 'DataFrame':
                    if daq_type in ["Pi", 0, 1]:
                        # ignore columns that don't have headers
                        # data.append((f, pd.read_csv(f, index_col=None, skiprows=skip_rows, names=["Time", "Loadcell_1", "Loadcell_2", "Loadcell_3", "Loadcell_4", "Loadcell_5", "Loadcell_6", "Loadcell_7", "Loadcell_8",  "Fx", "Fy", "Fz", "Cx", "Cy", "x1", "x2", "x3", "x4", "x5", "x6", "x7", "x8"])))
                        data.append((f, pd.read_csv(f, header=0)))
                        # data.append((f, pd.read_csv(f, skiprows=1)))
                    else: # remove first 200 rows of STM32/Nexus data, where a hammer was used for time sync
                        data.append((f, pd.read_csv(f, index_col=None, skiprows=skip_rows, names=["Fx", "Fy", "Fz", "Cx", "Cy", "Loadcell_1", "Loadcell_2", "Loadcell_3", "Loadcell_4", "Loadcell_5", "Loadcell_6", "Loadcell_7", "Loadcell_8", "Temp"])))
                        data[-1][1].insert(0, 'Time', np.arange(len(data[-1][1])) * 0.001)
                else:
                    data.append((f, np.loadtxt(f, delimiter=',', skiprows=1)))

    if len(data) == 0:
        raise ValueError('The supplied path(s) do not exist or do not contain any files')
    return data

def read_inferred_data(pathname, plate_names, output_type='DataFrame', skip_rows=1, usecols=None, names=None, rotation=None) -> list:
    """
    Read in the pre-inferred data from the path and return a list of tuples of the trial name(s), plate name(s) and the data
    
    Parameters
    ----------
    pathname : str | list
        The path to the file or folder to read in
    plate_names : list
        The names of the plates to read in (matching the filenames)
    output_type : str
        The type of data to return. Options are 'DataFrame' or 'numpy.ndarray'
    skip_rows : int
        The number of rows to skip when reading in the data
    usecols : list | NoneType
        The columns to read in
    names : list | NoneType
        The names to assign to the columns after reading in the data
    rotation : list | NoneType
        Array with which force axes should be multiplied to achieve the desired force orientation
            
    Returns
    -------
    data : list of tuples
        A list of tuples of the trial name and a list of tuples of plate names and dataframes - i.e. [("trial1", [("plate1", df), ("plate2", df)]), ("trial2", [("plate1", df), ("plate2", df)]), etc...]
    """
    files = []
    
    # Normalize pathname to a list
    if isinstance(pathname, str):
        pathname = [pathname]

    for path in pathname:
        if os.path.isdir(path):
            dir_files = [os.path.join(path, f) for f in os.listdir(path) if os.path.isfile(os.path.join(path, f))]
            files.extend(dir_files)
        elif os.path.isfile(path):
            files.append(path)

    if len(files) == 0:
        raise ValueError('The supplied path(s) do not exist or do not contain any files')

    # Extract the trials from the filenames
    trial_dict = {}
    for file in files:
        basename = os.path.basename(file)
        for plate_name in plate_names:
            if basename.startswith(plate_name):
                trial_name = basename[len(plate_name)+1:].split('.')[0]  # Get the part after the plate name and underscore
                if trial_name not in trial_dict:
                    trial_dict[trial_name] = []
                trial_dict[trial_name].append((plate_name, file))
                break

    # Read the data into dataframes or numpy arrays
    data = []
    for trial_name, plate_files in trial_dict.items():
        plate_data = []
        for plate_name, file in plate_files:
            print(f"Reading {file}")
            file_path = os.path.join(pathname, file) if isinstance(pathname, str) and os.path.isdir(pathname) else file
            df = pd.read_csv(file_path, skiprows=skip_rows, usecols=usecols, header=0)
            if names is not None:
                df.columns = names
            if rotation is not None:
                df["Fx"] *= rotation[0]
                df["Fy"] *= rotation[1]
                df["Fz"] *= rotation[2]
            if output_type == 'numpy.ndarray':
                df = df.to_numpy()
            plate_data.append((plate_name, df))
        data.append((trial_name, plate_data))

    return data

def read_nexus_csv_cols(filename, cols, names=None):
    """
    Read in the Nexus-captured data from the path and return it as a DataFrame
    
    Parameters
    ----------
    filename : str
        The path to the file to read in
    cols : list
        The columns which should be extracted - passed to pd.read_csv's usecols parameter
            
    Returns
    -------
    df : pd.DataFrame
        A dataframe holding the read in file's content
    """
    df = pd.read_csv(filename, 
        header=0,
        skiprows=[0,1,2,4],
        usecols=cols,
        names=names,
        index_col=False,
        )
    df.reset_index(inplace=True, drop=True)
    df = df.astype(float)
    return df

def filter(df, fs=1000, cutoff=100):
    """
    Low-pass the supplied dataframe with a 100 Hz, 4th-order Butterworth filter

    Parameters
    ----------
    df: pd.DataFrame
        The DataFrame to be filtered
    fs: int
        The sampling frequency at which the data was captured, by default 1000
    cutoff: int
        The cutoff frequency of the filter, by default 100 Hz

    Returns
    -------
    pd.DataFrame
        The filtered result
    
    """
    source_index = df.index
    sos = scipy.signal.butter(4, cutoff, fs=fs, output='sos') # 4th order LPF, 100 Hz
    filtered = scipy.signal.sosfiltfilt(sos, df.values, axis=0)
    return pd.DataFrame(filtered, columns=df.columns, index=source_index)

def highpass(df, fs=1000, cutoff=100):
    """
    High-pass the supplied dataframe with a 4th-order Butterworth filter to remove drift

    Parameters
    ----------
    df: pd.DataFrame
        The DataFrame to be filtered
    fs: int
        The sampling frequency at which the data was captured, by default 1000
    cutoff: int
        The cutoff frequency of the filter, by default 100 Hz

    Returns
    -------
    pd.DataFrame
        The filtered result
    
    """
    sos = scipy.signal.butter(4, cutoff, btype='highpass', fs=fs, output='sos') # 4th order LPF, 100 Hz
    filtered = scipy.signal.sosfilt(sos, df.values, axis=0)
    return pd.DataFrame(filtered, columns=df.columns)

def remove_clipped_loadcells(df, limits):
    loadcell_cols = [col for col in df.columns if col.startswith('Loadcell_')]
    for i, col in enumerate(loadcell_cols):
        df = df[df[col] > limits[0]].reset_index(drop=True) # remove all rows where the loadcell value is less than the lower limit
        df = df[df[col] < limits[1]].reset_index(drop=True) # remove all rows where the loadcell value is more than the upper limit
    return df

def correct_cop(df:pd.DataFrame, plate_height:float):
    """
    In cases where CoP comes from a source mounted below the plate, correct for the plate height. 
    Assumes plate deformation to be small - otherwise is invalid.
    Based on formulae at http://www.kwon3d.com/theory/grf/pad.html

    Parameters
    ----------
    df: pd.DataFrame
        The DataFrame to be corrected
    plate_height: float
        The height of the plate in mm

    Returns
    -------
    pd.DataFrame
        The corrected result
    """
    # get the force unit vectors
    n = np.sqrt(df['Fx']**2 + df['Fy']**2 + df['Fz']**2)
    df_unit = df.copy()
    df_unit['Fx'] = df['Fx'] / n
    df_unit['Fy'] = df['Fy'] / n
    df_unit['Fz'] = df['Fz'] / n

    # correct the CoP
    df['Cx'] = df['Cx'] + df_unit['Fx'] * plate_height / df_unit['Fz']
    df['Cy'] = df['Cy'] + df_unit['Fy'] * plate_height / df_unit['Fz']

    return df

def preprocess_data(
    df,
    force_cols=['Fx', 'Fy', 'Fz'],
    cop_cols=['Cx', 'Cy'],
    frame_lims=None,
    frame_spacing=1,
    filter_options=None
):
    """
    Preprocess force plate data by applying frame limits, spacing, and filtering.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame containing the force and COP data
    force_cols : list
        List of column names for force components (e.g., ['Fx', 'Fy', 'Fz'])
    cop_cols : list
        List of column names for center of pressure (e.g., ['Cx', 'Cy'])
    frame_lims : tuple, optional
        Start and end frame limits (e.g., (1000, 2000))
    frame_spacing : int, optional
        Spacing between frames to reduce data density
    filter_options : dict, optional
        Options for filtering the data. If None, no filtering is applied.
        Example: {'enabled': True, 'window_length': 11, 'polyorder': 3}

    Returns:
    --------
    tuple
        (processed_df, filter_info)
        processed_df: DataFrame with applied filters and frame limits
        filter_info: Dictionary with information about applied filters for title display
    """
    # Reset index to ensure we have integer indices
    df = df.reset_index(drop=True)

    # Create frames for the animation
    start_frame = frame_lims[0] if frame_lims is not None else 0
    end_frame = frame_lims[1] if frame_lims is not None else len(df)

    # Apply frame limits and spacing
    df = df[start_frame:end_frame:frame_spacing]
    df.index = df.index - df.index[0]

    filter_info = {}

    # Apply filtering if enabled
    if filter_options is not None and filter_options.get('enabled', False):
        # Get filter parameters
        window_length = filter_options.get('window_length', 11)
        polyorder = filter_options.get('polyorder', 3)

        # Ensure window_length is odd
        if window_length % 2 == 0:
            window_length += 1

        # Ensure window_length is less than the data length
        if window_length >= len(df):
            window_length = min(len(df) - 1, 5)
            if window_length % 2 == 0:
                window_length -= 1

        # Apply Savitzky-Golay filter to force columns if window_length is valid
        if window_length > polyorder and window_length >= 5:
            filtered_df = df.copy()
            for col in force_cols:
                filtered_df[col] = scipy.signal.savgol_filter(df[col], window_length, polyorder)

            # Also filter CoP columns if they exist
            for col in cop_cols:
                if col in df.columns:
                    # Only filter CoP values (not based on force threshold here)
                    if len(df) > window_length:  # Only filter if enough points
                        filtered_values = scipy.signal.savgol_filter(df[col], window_length, polyorder)
                        filtered_df[col] = filtered_values

            # Use the filtered dataframe
            df = filtered_df

            # Store filter information
            filter_info = {
                'applied': True,
                'window_length': window_length,
                'polyorder': polyorder
            }

    return df, filter_info

if __name__ == '__main__':
    # make a directory from the path string
    directory = 'data\platev01\Plate V01 - 2022-12-07' # directory where the files are located

    # read in the data
    data = read_data(directory)

    print(data)