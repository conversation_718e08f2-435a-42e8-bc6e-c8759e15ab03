{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import plotly.graph_objs as go\n", "import plotly.offline as py_offline\n", "from ipywidgets import interactive, VBox, HBox, SelectionSlider, IntSlider, Layout, Checkbox, Box, Button, Dropdown, FloatSlider\n", "import glob\n", "import os\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize offline mode\n", "py_offline.init_notebook_mode(connected=True)\n", "use_predefined_ranges = True\n", "\n", "# Directory containing CSV files\n", "csv_folder = 'data/plateo06.4/artifact'  # Update with your path\n", "\n", "# Read all CSV files in the directory\n", "csv_files = glob.glob(os.path.join(csv_folder, '*.csv'))\n", "dataframes = {os.path.basename(file): pd.read_csv(file) for file in tqdm(csv_files)}\n", "\n", "# Function to update the combined dataframe based on selected files\n", "def update_dataframe(selected_files):\n", "    selected_dfs = [dataframes[file] for file in selected_files]\n", "    return (pd.concat(selected_dfs, ignore_index=True) if selected_dfs else pd.DataFrame())[::1]\n", "\n", "# Initialize the dataframe with all data\n", "df = update_dataframe(dataframes.keys())\n", "# df = update_dataframe(['Plate03_Run03.csv'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Define the force plate dimensions\n", "plate_width = 500\n", "plate_height = 500\n", "\n", "# Create a Plotly FigureWidget for the 3D force vectors\n", "fig_3d = go.FigureWidget(layout=go.Layout(height=800, width=1000))\n", "scatter_3d = fig_3d.add_scatter3d(\n", "    x=[],\n", "    y=[],\n", "    z=[],\n", "    mode='markers',\n", "    marker=dict(size=1)\n", ")\n", "\n", "if use_predefined_ranges:\n", "    x_range = [-1000, 1000]\n", "    y_range = [-1000, 1000]\n", "    z_range = [0, 2500]\n", "else:\n", "    x_range = [df['Fx'].min(), df['Fx'].max()]\n", "    y_range = [df['Fy'].min(), df['Fy'].max()]\n", "    z_range = [df['Fz'].min(), df['Fz'].max()]\n", "\n", "# Calculate the aspect ratio\n", "x_span = abs(x_range[1] - x_range[0])\n", "y_span = abs(y_range[1] - y_range[0])\n", "z_span = abs(z_range[1] - z_range[0])\n", "\n", "# Normalize the spans to make an aspect ratio\n", "x_span /= max([x_span, y_span, z_span])\n", "y_span /= max([x_span, y_span, z_span])\n", "z_span /= max([x_span, y_span, z_span])\n", "\n", "# Set the aspect ratio to be consistent\n", "aspect_ratio = dict(x=x_span, y=y_span, z=z_span)\n", "\n", "fig_3d.update_layout(\n", "    scene=dict(\n", "        xaxis=dict(title='Fx', range=x_range, autorange=False),\n", "        yaxis=dict(title='Fy', range=y_range, autorange=False),\n", "        zaxis=dict(title='Fz', range=z_range, autorange=False),\n", "        aspectmode='manual',\n", "        aspectratio=aspect_ratio,\n", "    ),\n", "    title='3D Force Vectors',\n", ")\n", "\n", "# Create a Plotly FigureWidget for the 2D force plate\n", "fig_2d = go.FigureWidget(layout=go.Layout(height=400, width=400))\n", "all_points_2d = fig_2d.add_scatter(\n", "    x=df['Cx'], y=df['Cy'],\n", "    mode='markers',\n", "    marker=dict(size=2),\n", "    name='All Points'\n", ")\n", "selected_points_2d = fig_2d.add_scatter(\n", "    x=[], y=[],\n", "    mode='markers',\n", "    marker=dict(size=2, color='red'),\n", "    name='Selected Points'\n", ")\n", "segment_boundary = fig_2d.add_scatter(\n", "    x=[], y=[],\n", "    mode='lines',\n", "    line=dict(color='red', width=1),\n", "    fill=\"toself\",\n", "    fillcolor='rgba(0, 0, 255, 0.1)',\n", "    name='Segment Boundary'\n", ")\n", "\n", "fig_2d.update_layout(\n", "    title='Force Plate Segmentation',\n", "    xaxis=dict(\n", "        title='Cx (mm)',\n", "        range=[-250, 250],\n", "        autorange=False,\n", "        scaleanchor=\"y\",  # Ensure the x-axis is scaled according to the y-axis\n", "        scaleratio=1\n", "    ),\n", "    yaxis=dict(\n", "        title='Cy (mm)',\n", "        range=[-250, 250],\n", "        autorange=False\n", "    ),\n", "    showlegend=False\n", ")\n", "\n", "# Define the function to update the 3D plot based on the selected segment and slicing controls\n", "def update_plot(x_seg, y_seg, seg_size, selected_files):\n", "    global df\n", "    df = update_dataframe(selected_files)\n", "    \n", "    # Calculate segment boundaries\n", "    x_start = x_seg * seg_size - plate_width / 2\n", "    x_end = x_start + seg_size\n", "    y_start = y_seg * seg_size - plate_height / 2\n", "    y_end = y_start + seg_size\n", "    \n", "    # Filter the dataframe based on the selected segment\n", "    segment_data = df[(df['Cx'] >= x_start) & (df['Cx'] < x_end) & (df['Cy'] >= y_start) & (df['Cy'] < y_end)]\n", "    \n", "    # Update the 2D plot to highlight the selected segment\n", "    with fig_2d.batch_update():\n", "        fig_2d.data[1].x = segment_data['Cx']\n", "        fig_2d.data[1].y = segment_data['Cy']\n", "        fig_2d.data[2].x = [x_start, x_end, x_end, x_start, x_start]\n", "        fig_2d.data[2].y = [y_start, y_start, y_end, y_end, y_start]\n", "\n", "def update_slicing(slice_start, axis, thickness):\n", "    # Determine the end of the slice\n", "    slice_end = slice_start + thickness\n", "    \n", "    # Filter the dataframe based on the selected axis and slice range\n", "    if axis == 'Fx':\n", "        slice_data = df[(df['Fx'] >= slice_start) & (df['Fx'] < slice_end)]\n", "    elif axis == 'Fy':\n", "        slice_data = df[(df['Fy'] >= slice_start) & (df['Fy'] < slice_end)]\n", "    else:  # axis == 'Fz'\n", "        slice_data = df[(df['Fz'] >= slice_start) & (df['Fz'] < slice_end)]\n", "    \n", "    # Update the scatter plot data\n", "    with fig_3d.batch_update():\n", "        scatter_3d.data[0].x = slice_data['Fx']\n", "        scatter_3d.data[0].y = slice_data['Fy']\n", "        scatter_3d.data[0].z = slice_data['Fz']\n", "\n", "# Define the interactive controls\n", "seg_size_slider = IntSlider(value=50, min=10, max=100, step=10, description='Segment Size (mm)')\n", "\n", "def generate_segment_labels(seg_size):\n", "    x_segments = [(i * seg_size - plate_width / 2, (i + 1) * seg_size - plate_width / 2) for i in range(int(plate_width / seg_size))]\n", "    y_segments = [(i * seg_size - plate_height / 2, (i + 1) * seg_size - plate_height / 2) for i in range(int(plate_height / seg_size))]\n", "    x_labels = [f\"({x[0]:.1f},{x[1]:.1f})\" for x in x_segments]\n", "    y_labels = [f\"({y[0]:.1f},{y[1]:.1f})\" for y in y_segments]\n", "    return x_labels, y_labels\n", "\n", "def update_segment_sliders(*args):\n", "    x_labels, y_labels = generate_segment_labels(seg_size_slider.value)\n", "    x_seg_slider.options = x_labels\n", "    y_seg_slider.options = y_labels\n", "    x_seg_slider.value = x_labels[len(x_labels)//2]\n", "    y_seg_slider.value = y_labels[len(y_labels)//2]\n", "\n", "x_labels, y_labels = generate_segment_labels(seg_size_slider.value)\n", "x_seg_slider = SelectionSlider(options=x_labels, description='X Segment', value=x_labels[len(x_labels)//2])\n", "y_seg_slider = SelectionSlider(options=y_labels, description='Y Segment', value=y_labels[len(y_labels)//2])\n", "\n", "seg_size_slider.observe(update_segment_sliders, 'value')\n", "update_segment_sliders()  # Initialize slider options and values\n", "\n", "# Define the interactive controls for slicing\n", "axis_dropdown = Dropdown(options=['Fx', 'Fy', 'Fz'], value='Fx', description='Axis')\n", "thickness_slider = FloatSlider(value=100.0, min=1.0, max=5000.0, step=1.0, description='Thickness')\n", "start_slider = FloatSlider(value=0.0, min=-2000.0, max=2000.0, step=1.0, description='Slice Start')\n", "interactive_slicing = interactive(update_slicing, slice_start=start_slider, axis=axis_dropdown, thickness=thickness_slider)\n", "\n", "# Create checkboxes for CSV file selection\n", "checkboxes = [Checkbox(value=True, description=file) for file in dataframes.keys()]\n", "\n", "def get_selected_files():\n", "    return [checkbox.description for checkbox in checkboxes if checkbox.value]\n", "\n", "def update_plot_wrapper(*args):\n", "    selected_files = get_selected_files()\n", "    x_labels, y_labels = generate_segment_labels(seg_size_slider.value)\n", "    x_seg = x_labels.index(x_seg_slider.value)\n", "    y_seg = y_labels.index(y_seg_slider.value)\n", "    update_plot(x_seg, y_seg, seg_size_slider.value, selected_files)\n", "    update_slicing(slice_start=start_slider.value, axis=axis_dropdown.value, thickness=thickness_slider.value)\n", "\n", "# Add observers to checkboxes\n", "# for checkbox in checkboxes:\n", "#     checkbox.observe(update_plot_wrapper, 'value')\n", "\n", "# Button to manually trigger updates\n", "update_button = Button(description=\"Update Plots\")\n", "update_button.on_click(lambda b: update_plot_wrapper())\n", "\n", "# Create the interactive widget\n", "interactive_plot = VBox([x_seg_slider, y_seg_slider, seg_size_slider])\n", "\n", "# Define layout for the 2D plot and controls\n", "left_layout = Layout(width='350px')  # Fixed width for the 2D plot and controls\n", "\n", "# Create a box for checkboxes\n", "checkbox_box = VBox(checkboxes, layout=Layout(display='flex', flex_flow='column', align_items='stretch'))\n", "\n", "# Display the plots and interactive widget side by side with fixed widths\n", "display(HBox([VBox([fig_2d, interactive_plot, checkbox_box, update_button], layout=left_layout), VBox([fig_3d, interactive_slicing])]))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ml-env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 2}