from _io_utils import read_inferred_data, preprocess_data
import numpy as np
import plotly.graph_objects as go
import plotly.offline as py_offline
from tqdm import tqdm

# Define color constants to avoid magic numbers
COLORS = {
    'BACKGROUND': '#F6F5F1',        # White background
    'PAPER': '#F6F5F1',             # White paper background
    'DARK_GREEN': '#004225',        # Dark green for lines and text
    'LIME_GREEN': '#C2FF00',        # Bright lime green for highlights
    'GRAY': 'gray',                 # Gray for zero lines
    'LIGHT_GRAY': 'silver',         # Light gray for plate outline
    'TRANSPARENT': 'rgba(0, 0, 0, 0)',  # Transparent
    'SEMI_TRANSPARENT_WHITE': 'rgba(255, 255, 255, 0.5)',  # Semi-transparent white
    'BLACK': 'black',               # Black for background
    'WHITE': 'white',               # White for contours
}

options = {
    'pathnames': ['outputs/plateo06.3 CT Tests/Acinotech Forceplate 5 Lab Run Heel 2 R->L 20250424_152226.csv'],
    # 'pathnames': ['outputs/plateo06.3 CT Tests/Acinotech Forceplate 5 ARU 30cm hop 2 20250424_173500.csv'],
    'plates': ['Acinotech Forceplate 5'],
    'analysis_threshold': 50, # Fz threshold to be included in accuracy analysis
    'daq_version': 1, # ["STM32", "Pi", 0, 1] 0 for ESP32, 1 for EtherCAT
    'save': False, # save to outputs folder
    'plate_sizes': [(500,500)], # x,y
    'plate_centers': [(0, 0)], # x,y, relative to ground truth COP origin
    'camera_viewpoint': '39',
    'rotate_force_axes': [-1,-1,1], # swap axes to be reaction-oriented (force applied to body) rather than action-oriented (force applied to plate)
    'background': 'clear',
    'plate_view': 'contour',
    'frame_lims': (5500,7000), # None or start and end frames of the portion to animate
    # 'frame_lims': (12500,18000), # None or start and end frames of the portion to animate
    'show_axis_ticks': False, # Whether to show axis ticks and labels
    'frame_spacing': 1,
    'plot_arrow_head_history': True, # Whether to plot the cumulative arrow head trace
    'plot_sparse_vector_history': True, # Whether to plot the sparse force vector history
    'filter': {
        'enabled': False,
        'window_length': 21,
        'polyorder': 3
    }
}

if not options['daq_version'] in ["STM32", "Pi", 0, 1]:
    raise ValueError("Invalid DAQ version specified - must be one of ['STM32', 'Pi', 0, 1]")

if not options['background'] in ['clear', 'normal']:
    raise ValueError("Invalid background type specified - must be one of ['clear', 'normal']")

if not options['plate_view'] in ['none', 'contour', 'filled']:
    raise ValueError("Invalid plate view type specified - must be one of ['none', 'contour', 'filled']")

def plot_3d_animated(
    df,
    force_cols=['Fx', 'Fy', 'Fz'],
    cop_cols=['Cx', 'Cy'],
    title=None,
    plate_sizes=[(500, 500)],
    plate_centers=[(0, 0)],
    frame_spacing=20,
    plot_arrow_head_history=False,
    plot_sparse_vector_history=False,
    camera_viewpoint='163',
    show_axis_ticks=False,
    plate_view='contour',
    background='clear'
):
    """
    Create an animated 3D plot of force vectors and center of pressure.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame containing the preprocessed force and COP data
    force_cols : list
        List of column names for force components (e.g., ['Fx', 'Fy', 'Fz'])
    cop_cols : list
        List of column names for center of pressure (e.g., ['Cx', 'Cy'])
    title : str, optional
        Title for the plot
    plate_sizes : list of tuples
        List of (width, length) tuples for each force plate in mm
    plate_centers : list of tuples
        List of (x, y) center coordinates for each force plate
    frame_spacing : int
        Spacing between frames for animation
    plot_arrow_head_history : bool
        Whether to plot the cumulative arrow head trace
    plot_sparse_vector_history : bool
        Whether to plot the sparse force vector history
    camera_viewpoint : str
        Camera viewpoint identifier ('39', '163', '-y', '+x')
    show_axis_ticks : bool
        Whether to show axis ticks and labels
    plate_view : str
        Type of plate visualization ('none', 'contour', 'filled')
    background : str
        Background type ('clear', 'normal')

    Returns:
    --------
    plotly.graph_objects.Figure
        The plotly figure object
    """
    py_offline.init_notebook_mode(connected=True)

    scale = 0.6  # Scaling factor for the force vectors
    working_space_lims = [(0, 0), (0, 0), (0, 0)]

    # Set up plate visualization based on plate_view
    if plate_view == 'none':
        plate_contours = None
        plate_opacity = 0
    elif plate_view == 'contour':
        plate_contours = {
            "x": {"show": True, "color": COLORS['WHITE']},
            "y": {"show": True, "color": COLORS['WHITE']},
            "z": {"show": True, "color": COLORS['WHITE']},
        }
        plate_opacity = 0
    elif plate_view == 'filled':
        plate_contours = None
        plate_opacity = 0.5

    # Create figure
    fig = go.Figure()

    # Create plate traces
    plate_traces = []
    for i, (center, size) in enumerate(zip(plate_centers, plate_sizes)):
        x = [center[0] - size[0]/2, center[0] + size[0]/2]
        y = [center[1] - size[1]/2, center[1] + size[1]/2]
        z = [[0, 0], [0, 0]]

        working_space_lims[0] = (min(working_space_lims[0][0], min(x)), max(working_space_lims[0][1], max(x)))
        working_space_lims[1] = (min(working_space_lims[1][0], min(y)), max(working_space_lims[1][1], max(y)))

        plate_traces.append(go.Surface(
            x=x,
            y=y,
            z=z,
            contours=plate_contours,
            opacity=plate_opacity,
            showscale=False,
            name=f"Plate {i+1}",
            showlegend=False
        ))

    # Add plate traces
    fig.add_traces(plate_traces)

    # Initialize trace indices
    trace_indices = {}
    trace_idx = len(fig.data)  # Start after plate traces

    # Create cumulative arrow heads trace if enabled
    if plot_arrow_head_history:
        cumulative_arrow_heads_trace = go.Scatter3d(
            x=[],
            y=[],
            z=[],
            mode='lines',
            line=dict(color=COLORS['DARK_GREEN'], width=2),
            name='Arrow Head History',
            showlegend=True
        )
        fig.add_trace(cumulative_arrow_heads_trace)
        cumulative_arrow_heads_trace_idx = trace_idx
        trace_idx += 1
    else:
        cumulative_arrow_heads_trace_idx = None

    # Setup for sparse force vector traces
    sparse_force_vector_frames = []

    # Determine frame range
    start_frame = 0
    end_frame = len(df)
    frame_count = end_frame

    total_frames = len(range(start_frame, end_frame, frame_spacing))
    butterfly_spacing = 20

    # For each plate (assuming one plate for simplicity)
    center = plate_centers[0]  # Using first plate center

    # Create sparse force vector traces more efficiently if enabled
    if plot_sparse_vector_history:
        # Pre-calculate which frames will have sparse vectors
        sparse_frames = [t for t in range(start_frame, end_frame, frame_spacing)
                        if t % butterfly_spacing == 0]

        # Create a single trace for all sparse vectors
        for t in sparse_frames:
            # We'll create the actual traces during pre-computation phase
            sparse_force_vector_frames.append(t)
            trace_indices[f"force_{t}"] = trace_idx
            trace_idx += 1

            # Create placeholder traces that will be updated later
            force_vector_trace = go.Scatter3d(
                x=[None, None],
                y=[None, None],
                z=[None, None],
                mode='lines',
                line=dict(color=COLORS['LIGHT_GRAY'], width=4),
                name=f"Force Vector at t={t}",
                showlegend=False,
                visible=False  # Initially invisible
            )
            fig.add_trace(force_vector_trace)

    # Now add the current arrow head trace
    current_arrow_head_trace = go.Scatter3d(
        x=[None],
        y=[None],
        z=[None],
        mode='markers',
        marker=dict(size=3, color=COLORS['LIME_GREEN']),
        name='Current Arrow Head',
        showlegend=True
    )
    fig.add_trace(current_arrow_head_trace)
    current_arrow_head_trace_idx = trace_idx
    trace_idx += 1

    # Now add the current force vector trace (added last)
    current_force_vector_trace = go.Scatter3d(
        x=[None],
        y=[None],
        z=[None],
        mode='lines',
        line=dict(color=COLORS['LIME_GREEN'], width=10),
        name='Current Force Vector',
        showlegend=True
    )
    fig.add_trace(current_force_vector_trace)
    current_force_vector_trace_idx = trace_idx
    trace_idx += 1

    # Total number of traces
    total_traces = len(fig.data)

    # Pre-allocate frames list
    frames = []

    # Pre-compute all arrow positions for better memory management
    print("Pre-computing arrow positions...")

    # Create a mapping from frame number to position index for faster lookups
    frame_to_index = {}

    # Pre-allocate the positions array for better performance
    num_frames = len(range(start_frame, end_frame, frame_spacing))
    all_positions = []

    # Create a mapping for sparse vector frames
    sparse_vector_indices = {}
    if plot_sparse_vector_history:
        for i, t in enumerate(sparse_force_vector_frames):
            sparse_vector_indices[t] = i

    # Track max z value for working space limits
    max_head_z = 0

    # Pre-compute all positions in a single pass
    for frame_idx, t in enumerate(tqdm(range(start_frame, end_frame, frame_spacing))):
        frame_to_index[t] = frame_idx

        # Get the row at this frame
        row = df.iloc[t]

        # Offset Cx and Cy by the plate center
        offset_Cx = row[cop_cols[0]] + center[0]
        offset_Cy = row[cop_cols[1]] + center[1]

        # Calculate the head of the arrow
        head_x = offset_Cx + row[force_cols[0]] * scale
        head_y = offset_Cy + row[force_cols[1]] * scale
        head_z = row[force_cols[2]] * scale

        # Base of the arrow
        base_x = offset_Cx
        base_y = offset_Cy
        base_z = 0

        # Store positions
        positions_at_t = {
            'time': row['Time'] if 'Time' in row else t,
            'base_x': base_x,
            'base_y': base_y,
            'base_z': base_z,
            'head_x': head_x,
            'head_y': head_y,
            'head_z': head_z,
            'frame': t  # Store the original frame number
        }

        # Update working space limits
        working_space_lims[0] = (min(working_space_lims[0][0], head_x), max(working_space_lims[0][1], head_x))
        working_space_lims[1] = (min(working_space_lims[1][0], head_y), max(working_space_lims[1][1], head_y))
        max_head_z = max(max_head_z, head_z)

        all_positions.append(positions_at_t)

    # Update z working space limit once after all calculations
    working_space_lims[2] = (0, max_head_z)

    # Pre-compute sparse vector data if enabled
    if plot_sparse_vector_history and sparse_force_vector_frames:
        print("Pre-computing sparse vector data...")
        sparse_vector_data = {}

        for t in sparse_force_vector_frames:
            if t in frame_to_index:
                idx = frame_to_index[t]
                if idx < len(all_positions):
                    sparse_vector_data[t] = all_positions[idx]

    # Pre-allocate arrays for arrow head history if enabled
    if plot_arrow_head_history:
        # Pre-allocate with numpy for better performance
        max_points = len(all_positions)
        cumulative_arrow_heads_x = np.zeros(max_points)
        cumulative_arrow_heads_y = np.zeros(max_points)
        cumulative_arrow_heads_z = np.zeros(max_points)
    else:
        cumulative_arrow_heads_x = cumulative_arrow_heads_y = cumulative_arrow_heads_z = None

    print("Creating animation frames...")
    # Now create frames using pre-computed positions
    for t_idx, t in enumerate(tqdm(range(start_frame, end_frame, frame_spacing))):
        # List to hold updated traces
        frame_traces = []
        # Indices of traces that are updated in this frame
        updated_trace_indices = []

        # Get pre-computed positions for this frame
        pos = all_positions[t_idx]

        # Update cumulative arrow head positions if enabled
        if plot_arrow_head_history:
            # Only update the latest point instead of recalculating the entire history
            if t_idx > 0:
                # Just add the current point to the history
                cumulative_arrow_heads_x[t_idx] = pos['head_x']
                cumulative_arrow_heads_y[t_idx] = pos['head_y']
                cumulative_arrow_heads_z[t_idx] = pos['head_z']
            else:
                # First frame, initialize the first point
                cumulative_arrow_heads_x[0] = pos['head_x']
                cumulative_arrow_heads_y[0] = pos['head_y']
                cumulative_arrow_heads_z[0] = pos['head_z']

            # Update cumulative arrow heads trace if enabled
            if cumulative_arrow_heads_trace_idx is not None:
                # Create a view of the arrays up to the current point
                x_view = cumulative_arrow_heads_x[:t_idx+1]
                y_view = cumulative_arrow_heads_y[:t_idx+1]
                z_view = cumulative_arrow_heads_z[:t_idx+1]

                cumulative_arrow_heads_trace_data = dict(
                    type='scatter3d',
                    x=x_view,  # Only use the points up to current frame
                    y=y_view,
                    z=z_view
                )
                frame_traces.append(cumulative_arrow_heads_trace_data)
                updated_trace_indices.append(cumulative_arrow_heads_trace_idx)

        # Update current arrow head trace
        current_arrow_head_trace_data = dict(
            type='scatter3d',  # Specify the trace type
            x=[pos['head_x']],
            y=[pos['head_y']],
            z=[pos['head_z']]
        )
        frame_traces.append(current_arrow_head_trace_data)
        updated_trace_indices.append(current_arrow_head_trace_idx)

        # Update current force vector trace
        current_force_vector_trace_data = dict(
            type='scatter3d',  # Specify the trace type
            x=[pos['base_x'], pos['head_x']],
            y=[pos['base_y'], pos['head_y']],
            z=[pos['base_z'], pos['head_z']]
        )
        frame_traces.append(current_force_vector_trace_data)
        updated_trace_indices.append(current_force_vector_trace_idx)

        # Update sparse force vector traces if enabled
        if plot_sparse_vector_history and sparse_force_vector_frames:
            # Calculate the base index for sparse vector traces
            base_idx = 0 if cumulative_arrow_heads_trace_idx is None else cumulative_arrow_heads_trace_idx + 1

            # Update both visibility and data for sparse vectors in one pass
            for fv_idx, fv_t in enumerate(sparse_force_vector_frames):
                force_vector_trace_idx = base_idx + fv_idx

                # Only update if this vector should be visible at current time
                if fv_t <= t:
                    # Use pre-computed sparse vector data for better performance
                    if 'sparse_vector_data' in locals() and fv_t in sparse_vector_data:
                        sparse_pos = sparse_vector_data[fv_t]

                        # Update both data and visibility in one operation
                        update = dict(
                            type='scatter3d',
                            visible=True,
                            x=[sparse_pos['base_x'], sparse_pos['head_x']],
                            y=[sparse_pos['base_y'], sparse_pos['head_y']],
                            z=[sparse_pos['base_z'], sparse_pos['head_z']]
                        )
                    else:
                        # Just update visibility if we don't have position data
                        update = dict(
                            type='scatter3d',
                            visible=True
                        )
                else:
                    # Hide vectors that shouldn't be visible yet
                    update = dict(
                        type='scatter3d',
                        visible=False
                    )

                frame_traces.append(update)
                updated_trace_indices.append(force_vector_trace_idx)

        # Build the frame
        frame = go.Frame(
            data=frame_traces,
            traces=updated_trace_indices,
            name=str(t),
            layout=go.Layout(annotations=[
                dict(
                    text=f"Time: {pos['time']:.2f} s",
                    showarrow=False,
                    xref='paper',
                    yref='paper',
                    x=0.5,
                    y=0.9,
                    font=dict(color=COLORS['WHITE'])
                )
            ])
        )

        frames.append(frame)

    # Set fixed camera view
    if camera_viewpoint == '39':  # camera behind plate
        camera = dict(
            up=dict(x=0, y=0, z=1),
            center=dict(x=0, y=0, z=-0.1),
            eye=dict(x=0.01, y=-3, z=-0.3)
        )
    elif camera_viewpoint == '163':  # camera behind plate
        camera = dict(
            up=dict(x=0, y=0, z=1),
            center=dict(x=0, y=0, z=-0.1),
            eye=dict(x=-0.03, y=-3, z=-0.26)
        )
    elif camera_viewpoint == '-y':  # camera behind plate
        camera = dict(
            up=dict(x=0, y=0, z=1),
            center=dict(x=0, y=0, z=-0.1),
            eye=dict(x=0.01, y=-2, z=-0.1)
        )
    elif camera_viewpoint == '+x':  # camera right of plate
        camera = dict(
            up=dict(x=0, y=0, z=1),
            center=dict(x=0, y=0, z=0),
            eye=dict(x=2, y=-0.01, z=-0.2)
        )

    # Expand working space for a little breathing room at the edges
    margin = 50
    working_space_lims[0] = (working_space_lims[0][0] - margin, working_space_lims[0][1] + margin)
    working_space_lims[1] = (working_space_lims[1][0] - margin, working_space_lims[1][1] + margin)
    working_space_lims[2] = (0, working_space_lims[2][1] + margin)

    showbackground = background == 'normal'

    fig.update_layout(
        title=title,
        paper_bgcolor=COLORS['BLACK'],
        scene=dict(
            xaxis_title="Cx (mm)" if show_axis_ticks else "",
            yaxis_title="Cy (mm)" if show_axis_ticks else "",
            zaxis_title="Force (N)" if show_axis_ticks else "",
            xaxis=dict(
                range=working_space_lims[0],
                showbackground=showbackground,
                showgrid=showbackground,
                zeroline=showbackground,
                showticklabels=show_axis_ticks,
                showspikes=show_axis_ticks
            ),
            yaxis=dict(
                range=working_space_lims[1],
                showbackground=showbackground,
                showgrid=showbackground,
                zeroline=showbackground,
                showticklabels=show_axis_ticks,
                showspikes=show_axis_ticks
            ),
            zaxis=dict(
                range=working_space_lims[2],
                showbackground=showbackground,
                showgrid=showbackground,
                zeroline=showbackground,
                showticklabels=show_axis_ticks,
                showspikes=show_axis_ticks
            ),
            camera=camera,
            aspectmode='cube',
        ),
        updatemenus=[dict(
            type="buttons",
            showactive=False,
            buttons=[
                dict(label="Play",
                     method="animate",
                     args=[None, {
                         "frame": {"duration": 10, "redraw": True},
                         "fromcurrent": True,
                         "mode": "immediate",
                         "transition": {"duration": 0}
                     }]),
                dict(label="Pause",
                     method="animate",
                     args=[[None], {
                         "frame": {"duration": 0, "redraw": False},
                         "mode": "immediate",
                         "transition": {"duration": 0}
                     }])
            ]
        )],
        # Add a slider for better navigation
        sliders=[{
            'active': 0,
            'yanchor': 'top',
            'xanchor': 'left',
            'currentvalue': {
                'font': {'size': 16},
                'prefix': 'Frame: ',
                'visible': True,
                'xanchor': 'right'
            },
            'transition': {'duration': 0},
            'pad': {'b': 10, 't': 50},
            'len': 0.9,
            'x': 0.1,
            'y': 0,
            'steps': [
                {
                    'args': [
                        [str(t)],
                        {
                            'frame': {'duration': 0, 'redraw': False},
                            'mode': 'immediate',
                            'transition': {'duration': 0}
                        }
                    ],
                    'label': str(t),
                    'method': 'animate'
                }
                # Only create slider steps for a subset of frames to improve performance
                for t in range(start_frame, end_frame, frame_spacing * 10)
            ]
        }]
    )

    # Update the layout to have a dark theme
    fig.update_layout(template='plotly_dark')

    # Add frames to the figure
    fig.frames = frames

    py_offline.plot(fig)
    return fig

if __name__ == '__main__':
    # Define column names
    columns_to_use = ['Time', 'Fx', 'Fy', 'Fz', 'Cx', 'Cy']
    output_names = ['Time', 'Fx', 'Fy', 'Fz', 'Cx', 'Cy']
    forces = ['Fx', 'Fy', 'Fz']
    CoPs = ['Cx', 'Cy']

    # Load the data
    data = read_inferred_data(
        options['pathnames'],
        options['plates'],
        skip_rows=0,
        usecols=columns_to_use,
        names=output_names,
        rotation=options['rotate_force_axes']
    )

    # Process each trial separately
    for i, (trialname, plates) in enumerate(data):
        # Plot each plate separately
        for idx, (plate, df) in enumerate(plates):
            # Step 1: Preprocess the data
            processed_df, filter_info = preprocess_data(
                df=df,
                force_cols=forces,
                cop_cols=CoPs,
                frame_lims=options['frame_lims'],
                frame_spacing=options['frame_spacing'],
                filter_options=options['filter']
            )

            # Step 2: Create title with filter information if filtering was applied
            title = f"{trialname} - {plate}"
            if filter_info.get('applied', False):
                window_length = filter_info['window_length']
                polyorder = filter_info['polyorder']
                title = f"{title} (Filtered: SG w={window_length}, p={polyorder})"

            # Step 3: Plot the preprocessed data
            plot_3d_animated(
                df=processed_df,
                force_cols=forces,
                cop_cols=CoPs,
                title=title,
                plate_sizes=options['plate_sizes'],
                plate_centers=options['plate_centers'],
                frame_spacing=options['frame_spacing'],
                plot_arrow_head_history=options['plot_arrow_head_history'],
                plot_sparse_vector_history=options['plot_sparse_vector_history'],
                camera_viewpoint=options['camera_viewpoint'],
                show_axis_ticks=options['show_axis_ticks'],
                plate_view=options['plate_view'],
                background=options['background']
            )