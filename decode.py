from _io_utils import read_data, filter, highpass, remove_clipped_loadcells, correct_cop
# from _zero import zero, threshold_cop, remove_hysteresis, get_zero
from _loadcell_trimmer import trim_loadcells
from _perf import analyse_model_perf
from _zero import zero
import numpy as np
import os
from pathlib import Path
import pandas as pd
import onnxruntime as rt
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
import joblib
import plotly.graph_objects as go
import plotly.offline as py_offline
import torch
from plot_2d_animated import plot_2d_animated
from plot_3d_animated import plot_3d_animated, preprocess_data

options = {
    # 'pathnames': ['data/platev05.19/Concrete Unbolted'],
    'pathnames': ['data/plateo06.4/Tests'],
    # 'pathnames': ['data/platev04.5/Concrete Bolted No Zero'],
    'analysis_threshold': 50, # Fz threshold to be included in accuracy analysis
    'model': 'models/obi-wan06.4/radiant-river-2786', #helpful-shape-98
    # 'model': 'models/vader05.19/V05.19_clear-darkness-2744', #helpful-shape-98
    # 'model': 'models/vader03.4/absurd-puddle-131', #helpful-shape-98
    'daq_version': 1, # ["STM32", "Pi", 0, 1] 0 for ESP32, 1 for EtherCAT
    'channel_start': 0,
    'loadcell_count': 6,
    'daq_channel_count': 8,
    'plot_comparison': True,
    'plot_analysis': False,
    'plot_2d_animated': False,
    'plot_3d_animated': False,
    'save': False, # save to outputs folder
    'zero_loadcells': True,
    'zero_output': False,
    'plate-size': (500,500), # x,y
    'plate_center': (0, 0), # x,y, relative to ground truth COP origin
    'engineered_features': [],
    'use_physics': False,
    'filter': True,
    'scale_in_model': True,
}

if not options['daq_version'] in ["STM32", "Pi", 0, 1]:
    raise ValueError("Invalid DAQ version specified - must be one of ['STM32', 'Pi', 0, 1]")

if __name__ == '__main__':
    whole_set = pd.DataFrame()
    plate_name = options['pathnames'][0].split('/')[-1]
    model_name = options['model'].split('/')[-1]
    output_dir = Path('outputs') / plate_name / model_name

    # Load the model
    sess = rt.InferenceSession(options['model'] + '.onnx')
    if not options['scale_in_model']:
        x_scaler = joblib.load(options['model'] + '.xscl')
        y_scaler = joblib.load(options['model'] + '.yscl')

    # Get the input and output names
    print(sess.get_inputs())
    input_name = sess.get_inputs()[0].name
    output_name = sess.get_outputs()[0].name

    # Get the input and output dimension
    input_dim = sess.get_inputs()[0].shape[1] # 8
    output_dim = sess.get_outputs()[0].shape[1] # 5

    # Load the data
    data = read_data(options['pathnames'], daq_type=options['daq_version'], skip_rows=1)

    nearest_loc = ""
    nearest_dist = 10000000

    # Process the data
    for (i, (fullpath, df)) in enumerate(data):
        print(f"Processing file {i+1}/{len(data)}: " + fullpath)
        # if i not in set([1,2,3,4]): continue
        df = df.dropna(axis=0).reset_index(drop=True)
        has_ground_truth = 'Fx' in df.columns

        if has_ground_truth:
            # correct the COP by subtracting the plate center from Cx and Cy
            df[['Cx', 'Cy']] = df[['Cx', 'Cy']] - options['plate_center']

        # for i in range(9):
        #     df = df.rename(columns={f'Loadcell {i}': f'Loadcell_{i}'})

        # remove extra measurement columns (last 8)
        # df = df.iloc[:, :-8]
        df.columns = df.columns.str.replace(' ', '_')

        # trim unused loadcell channels
        if options['daq_version'] == 0 or options['daq_version'] == 1:
            channels = []
            # get the column index corresponding to the first loadcell
            # first_loadcell_ind = data[0][1].columns.get_loc('Loadcell_' + str(options['channel_start']))
            first_loadcell_ind = options['channel_start']
            for j in range(options['loadcell_count']):
                channels.append(first_loadcell_ind + j)

            print(channels)

            to_trim = [j for j in range(first_loadcell_ind, options['daq_channel_count']) if j not in channels]
            to_trim = ['Loadcell_' + str(k) for k in to_trim]
            print(f"Trimming loadcells {to_trim}")
            df = trim_loadcells(df, to_trim)

        # Get rid of LC 8 points near 65000 - which seems a common error code
        if (options['loadcell_count'] == 8):
            df = df[abs(df['Loadcell_8']-65000)>2000].reset_index(drop=True)

        # get just the loadcell data to pass to the model
        loadcell_cols = [col for col in df.columns if col.startswith('Loadcell_')]

        zero_point = df[loadcell_cols].iloc[:10].mean() # get zero point for engineered features before applying zero
        df[loadcell_cols] = df[loadcell_cols]# - [-4000000,-4000000, 0,-4000000,0,-4000000]
        # zero the data
        if options['zero_loadcells']:
            if has_ground_truth:
                df = zero(df, length=100, loadcells=True)
            else:
                df = zero(df, loadcell_cols, 100, None)

        if options['engineered_features'] is not None and 'zero_point' in options['engineered_features'] :
            # add columns for zero point of each loadcell
            for col in loadcell_cols:
                df[col + "_zero_point"] = zero_point[col]

            loadcell_cols = loadcell_cols + [col + "_zero_point" for col in loadcell_cols]
            loadcell_data = df[loadcell_cols].copy()

        if options['engineered_features'] is None or len(options['engineered_features']) == 0:
            loadcell_data = df[loadcell_cols].copy()

        # filter the loadcell data
        if options['filter']:
            loadcell_data = filter(loadcell_data, 1000, 10)
        loadcell_data = loadcell_data.to_numpy()

        # replace loadcell data with zero frames
        # loadcell_data[:1000] = [0,0,0,0,0,0]

        # # append zero_point to all frames of the loadcell data
        # zero_point = [-2821782,3668480,-766193,-3618289,6401860,2079283]
        # zero_point_expanded = np.ones((loadcell_data.shape[0], 6)) * zero_point
        # loadcell_data = loadcell_data + zero_point
        # loadcell_data = np.concatenate((loadcell_data, zero_point_expanded), axis=1)

        # reshape loadcell data to be 2D
        print(f"Using columns {loadcell_cols}")
        # loadcell_data = df[loadcell_cols].to_numpy()
        loadcell_data = loadcell_data.reshape(-1, input_dim).astype(np.float32)
        print(loadcell_data.shape)

        if options['use_physics']:
            EXCITATION = 12
            counts_to_V_conversion = 1.2 / (8388607 * np.array([64,64,128,64,128,64]))
            v_to_kg_conversion = np.array([200,200,100,200,100,200]) / (np.array([0.002, 0.002, 0.001, 0.002, 0.001, 0.002]) * EXCITATION)
            conv = counts_to_V_conversion * v_to_kg_conversion * 9.81

            Fx = (conv * loadcell_data)[:, [4]] * 6 / 1
            Fy = (conv * loadcell_data)[:, [2]] * 6 / 1
            Fz = (conv * loadcell_data)[:, [0, 1, 3, 5]].sum(axis=1, keepdims=True) * 6 / 4
            Cx = np.zeros(((conv * loadcell_data).shape[0], 1))
            Cy = np.zeros(((conv * loadcell_data).shape[0], 1))
            physics = np.concatenate((Fx, Fy, Fz, Cx, Cy), axis=1)

            print(physics)
            y_scaler = joblib.load(options['model'] + '.yscl')
            physics = y_scaler.inverse_transform(physics)

        # scale input, predict, and unscale the output
        if options['scale_in_model']: # scaling done in-model
            output = sess.run([output_name], {input_name: loadcell_data})[0]
        else:
            loadcell_data = x_scaler.transform(loadcell_data)
            output = sess.run([output_name], {input_name: loadcell_data})[0]
            output = y_scaler.inverse_transform(output)

        if options['use_physics']:
            output += physics

        # make a dataframe from the output
        sep = ',' if ',' in output_name else '_'
        output_names = [name + '_pred' for name in output_name.split(sep)]
        output_df = pd.DataFrame(output, columns=output_names)

        # make a new dataframe to compare the model output to the original data and save
        output_df = pd.concat([df, output_df], axis=1)
        if has_ground_truth:
            output_df = output_df.rename(columns={'Fx': 'Fx_true', 'Fy': 'Fy_true', 'Fz': 'Fz_true', 'Cx': 'Cx_true', 'Cy': 'Cy_true'})

        if options['plot_comparison'] and has_ground_truth:
            if (fullpath.endswith("Flipped.csv")):
                plt.plot(-output_df['Fx_pred'], 'r', label='Fx_pred', linewidth=0.8)
            elif (fullpath.endswith("Rotated.csv")):
                plt.plot(-output_df['Fy_pred'], 'r', label='Fx_pred', linewidth=0.8)
            else:
                plt.plot(output_df['Fx_pred'], 'r', label='Fx_pred', linewidth=0.8)
            plt.plot(output_df['Fx_true'], 'r--', label='Fx_true', linewidth=0.5)
            # plt.title(f"{i+1}: {fullpath} - Fx")
            # plt.legend()
            # plt.show()

            if (fullpath.endswith("Flipped.csv")):
                plt.plot(-output_df['Fy_pred'], 'g', label='Fy_pred', linewidth=0.8)
            elif (fullpath.endswith("Rotated.csv")):
                plt.plot(output_df['Fx_pred'], 'g', label='Fy_pred', linewidth=0.8)
            else:
                plt.plot(output_df['Fy_pred'], 'g', label='Fy_pred', linewidth=0.8)
            plt.plot(output_df['Fy_true'], 'g--', label='Fy_true', linewidth=0.5)
            # plt.title(f"{i+1}: {fullpath} - Fy")
            # plt.legend()
            # plt.show()

            plt.plot(output_df['Fz_pred'], 'b', label='Fz_pred', linewidth=0.8)
            plt.plot(output_df['Fz_true'], 'b--', label='Fz_true', linewidth=0.5)
            plt.title(f"{i+1}: {fullpath}")
            plt.legend()
            plt.show()

            plt.plot(output_df['Fy_pred']-output_df['Fy_true'], label=f'Fy_pred Error %', linewidth=0.8)
            # plt.plot(100*output_df['Fy_true'].diff(10)/output_df['Time'].diff(10), label=f'100(dFy/dt)', linewidth=0.8)
            # plt.plot(output_df['Fz_true']-700, label=f'(Fz_true)-700 {fullpath}', linewidth=0.8)
            # plt.plot(output_df['Fy_pred']-output_df['Fy_true'], label=f'Fy_pred_{i}', linewidth=0.8)
            # plt.plot(output_df['Fz_pred']-output_df['Fz_true'], label=f'Fz_pred_{i}', linewidth=0.8)
            plt.title(f"{i+1}: {fullpath}")
            plt.legend()
            plt.show()

            plt.plot(output_df['Cx_true'], label='Cx_true')
            plt.plot(output_df['Cx_pred'], label='Cx_pred')
            plt.title(f"{i+1}: {fullpath} - Cx")
            plt.legend()
            plt.show()

            plt.plot(output_df['Cy_true'], label='Cy_true')
            plt.plot(output_df['Cy_pred'], label='Cy_pred')
            plt.title(f"{i+1}: {fullpath} - Cy")
            plt.legend()
            plt.show()
        elif options['plot_comparison']:
            if options['zero_output']:
                # drop Fx, Fy, Fz columns
                output_df = output_df.drop(columns=['Fx', 'Fy', 'Fz'])
                # rename Fx_pred, Fy_pred, Fz_pred to Fx, Fy, Fz for zeroing and back again
                output_df = output_df.rename(columns={'Fx_pred': 'Fx', 'Fy_pred': 'Fy', 'Fz_pred': 'Fz'})
                output_df = zero(output_df, length=10, forces=True, loadcells=False)
                output_df = output_df.rename(columns={'Fx': 'Fx_pred', 'Fy': 'Fy_pred', 'Fz': 'Fz_pred'})

            loadcell_df = pd.DataFrame(loadcell_data, columns=[f'Loadcell_{i+1}' for i in range(loadcell_data.shape[1])])

            if True:
                loadcell_df.index = output_df["Time"] - output_df["Time"][0]
                output_df.index = output_df["Time"] - output_df["Time"][0]

            # Create a new figure with two subplots
            from plotly.subplots import make_subplots
            fig = make_subplots(rows=2, cols=1, shared_xaxes=True, subplot_titles=["Force Components", "Load Cell Data"])

            # Define the force components and CoP components
            forces = ['Fx_pred', 'Fy_pred', 'Fz_pred']
            # forces = ['Fx_pred']
            CoPs = []  # ['Cx_pred', 'Cy_pred']

            # Add traces for each force component with custom hover information including CoP values
            for force in forces:
                # Construct the tooltip text
                tooltip_text = output_df.apply(
                    lambda row: '<br>'.join([f"{col}: {row[col]:.2f}" for col in loadcell_df.columns] +
                                            [f"{f}: {row[f]:.2f}" for f in forces + CoPs] +
                                            [f"Time: {row['Time']-output_df['Time'][0]:.3f} s"]), axis=1)

                fig.add_trace(go.Scatter(x=output_df.index, y=output_df[force], mode='lines', name=force,
                                        hoverinfo='text',
                                        text=tooltip_text),
                            row=1, col=1)  # Add to the first subplot

            # Add traces for loadcell data
            for col in loadcell_df.columns:
                fig.add_trace(go.Scatter(x=loadcell_df.index, y=loadcell_df[col], mode='lines', name=col),
                            row=2, col=1)  # Add to the second subplot

            # Update layout with title and axis labels
            fig.update_layout(title=f"{i+1}: {fullpath}, processed with {model_name}",
                            xaxis_title="Time (s)",
                            yaxis_title="F (N)",
                            legend_title="Components",
                            hovermode="closest")

            # Update y-axis labels for each subplot
            fig.update_yaxes(title_text="Force Components (N)", row=1, col=1)
            fig.update_yaxes(title_text="Load Cell Output", row=2, col=1)

            # Show the figure
            py_offline.plot(fig)

        # plotly animation - looks nice for video overlay but has to be manually saved
        # makes a line plot of forces and scatter of CoP over time
        if options['plot_2d_animated']:
            output_df = output_df.reset_index(drop=True)

            # Define the force components and their colors
            forces = ['Fx_pred', 'Fy_pred', 'Fz_pred']
            colors = {'Fx_pred': 'grey', 'Fy_pred': 'black', 'Fz_pred': 'orange'}

            plot_2d_animated(output_df, forces, ['Cx_pred', 'Cy_pred'], title=f"{i+1}: {fullpath}", force_colors=colors)

        # 3D animated visualization of force vectors
        if options['plot_3d_animated']:
            output_df = output_df.reset_index(drop=True)

            # Plot the 3D visualization
            plot_3d_animated(
                df=output_df,
                force_cols=['Fx_pred', 'Fy_pred', 'Fz_pred'],
                cop_cols=['Cx_pred', 'Cy_pred'],
                title=f"{i+1}: {fullpath}",
                plate_sizes=[options['plate-size']],
                plate_centers=[options['plate_center']],
                frame_spacing=20,
                plot_arrow_head_history=True,
                plot_sparse_vector_history=False,
                camera_viewpoint='163',
                show_axis_ticks=True,
            )

        #     plt.scatter(output_df['Cx_pred'], output_df['Cy_pred'])
        #     plt.xlabel("Cx (mm)")
        #     plt.ylabel("Cy (mm)")
        #     plt.xlim(-options['plate-size'][0]/2, options['plate-size'][0]/2)
        #     plt.ylim(-options['plate-size'][1]/2, options['plate-size'][1]/2)
        #     plt.title(f"{i+1}: {fullpath}")
        #     plt.show()

        if options['save']:
            filename = Path(fullpath).stem + '_decoded.csv'
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            output_df.to_csv(output_dir / filename, index=False, float_format='%.3f')

        whole_set = pd.concat([whole_set, output_df], axis=0, ignore_index=True)

    plt.legend()
    plt.show()

    if (has_ground_truth and options['plot_analysis']):
        print(whole_set.head)
        whole_set = whole_set.where(whole_set['Fz_true'] > options['analysis_threshold']).dropna()
        Y_pred = whole_set[['Fx_pred', 'Fy_pred', 'Fz_pred', 'Cx_pred', 'Cy_pred']].to_numpy()
        Y_true = whole_set[['Fx_true', 'Fy_true', 'Fz_true', 'Cx_true', 'Cy_true']].to_numpy()
        Y_CoPs = whole_set[['Cx_true', 'Cy_true']].copy().rename(columns={'Cx_true': 'Cx', 'Cy_true': 'Cy'})
        analyse_model_perf(Y_pred, Y_true, Y_CoPs, target_cols=output_name.split(','), plate_size=options['plate-size'], plot=True, threshold=10)