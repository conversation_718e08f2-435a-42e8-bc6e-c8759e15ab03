import pandas as pd
from multipledispatch import dispatch
import numpy as np

options = {
    'pathname': 'data\platev01\Plate V01 - 2023-01-25', # file or folder to read in
    'daq_version': 0,
    'loadcell_count': 6,
    'channel_start': 1
}

@dispatch(pd.DataFrame, list, int, (type(None),list))
def zero(df:pd.DataFrame, columns:list, length:int=1000, zero_points=None) -> pd.DataFrame:
    """Zero the data in the given columns by subtracting the mean of the first `length` samples.
    
    Parameters
    ----------
    df : pd.DataFrame
        Dataframe containing the data to be zeroed.
    columns : list of str
        List of column names to be zeroed.
    length : int, optional
        Number of samples to use to calculate the mean, by default 1000
    zero_points : list, optional
        List of force zero points to subtract from the data, by default None
    
    Returns
    -------
    pd.DataFrame
        Dataframe with the given columns zeroed.
    """

    if zero_points is None:
        for col in columns:
            # get the mean of the first `length` samples and subtract it from the data
            df[col] = df[col] - df[col][:length].mean()
    else:
        for col, zero_point in zip(columns, zero_points):
            # subtract the zero point from the data
            df[col] = df[col] - zero_point

    return df

@dispatch(pd.DataFrame, length=int, forces=bool, cops=bool, loadcells=bool, f_zero_points=list, c_zero_points=list, l_zero_points=list)
def zero(df:pd.DataFrame, length=1000, forces=True, cops=False, loadcells=True, f_zero_points=None, c_zero_points=None, l_zero_points=None) -> pd.DataFrame:
    """Zero the force and/or loadcell data in the given columns by subtracting the mean of the first `length` samples.
    
    Parameters
    ----------
    df : pd.DataFrame
        Dataframe containing the data to be zeroed.
    forces : bool, optional
        Should the force columns be zeroed, by default True
    cops : bool, optional
        Should the center of pressure columns be zeroed, by default False
    loadcells : bool, optional
        Should the loadcell columns be zeroed, by default True
    length : int, optional
        Number of samples to use to calculate the mean, by default 1000
    f_zero_points : list, optional
        List of force zero points to subtract from the data, by default None
    c_zero_points : list, optional
        List of center of pressure zero points to subtract from the data, by default None
    l_zero_points : list, optional
        List of loadcell zero points to subtract from the data, by default None
    
    Returns
    -------
    pd.DataFrame
        Dataframe with the given columns zeroed.
    """

    if forces:
        # zero force data
        df = zero(df, ['Fx', 'Fy', 'Fz'], length, f_zero_points)

    if cops:
        df = zero(df, ['Cx', 'Cy'], length, c_zero_points)

    if loadcells:
        # zero loadcell data
        loadcell_cols = [col for col in df.columns if col.startswith('Loadcell_')]
        df = zero(df, loadcell_cols, length, l_zero_points)

    return df

def get_zero(df, forces=True, loadcells=True, length=100):
    """Get the zero points of the force and/or loadcell data in the given columns by subtracting the mean of the first `length` samples.
    
    Parameters
    ----------
    df : pd.DataFrame
        Dataframe containing the data to be zeroed.
    forces : bool, optional
        Should the force columns be zeroed, by default True
    loadcells : bool, optional
        Should the loadcell columns be zeroed, by default True
    length : int, optional
        Number of samples to use to calculate the mean, by default 1000
    
    Returns
    -------
    list
        The zero points of each column
    """

    f_zero_points = []
    l_zero_points = []

    if forces:
        # zero force data
        for col in ['Fx', 'Fy', 'Fz']:
            # get the mean of the first `length` samples
            f_zero_points.append(df[col][:length].mean())

    if loadcells:
        # zero loadcell data
        loadcell_cols = [col for col in df.columns if col.startswith('Loadcell_')]
        for col in loadcell_cols:
            # get the mean of the first `length` samples
            l_zero_points.append(df[col][:length].mean())

    if forces and loadcells:
        return f_zero_points, l_zero_points
    elif forces:
        return f_zero_points
    elif loadcells:
        return l_zero_points

def threshold_cop(df, threshold=50):
    """Set the CoP to the origin if the Fz is below the threshold.
    
    Parameters
    ----------
    df : pd.DataFrame
        Dataframe containing the data to be zeroed.
    threshold : int, optional
        Threshold for the Fz, by default 50
        
    Returns
    -------
    pd.DataFrame
        Dataframe with the CoP set to the origin if the Fz is below the threshold.
    """

    df.loc[df['Fz'] < threshold, ['Cx', 'Cy']] = 0

    return df

def remove_hysteresis(df, threshold=20):
    """ Remove hysteresis in the loadcells
    Find all segments where the applied force (Fz) is below the threshold
    Use these segments to calculate the mean of the loadcells and subtract it from the data until the next segment is found.
    """
    loadcell_cols = [col for col in df.columns if col.startswith('Loadcell_')]
    ldf = df[loadcell_cols].copy()

    # Find all the segments where the force is below the threshold
    segments = []
    start = 0
    buffer = 10
    for i in range(len(ldf)):
        if abs(df['Fz'][i]) < threshold:
            if i == len(ldf) - 1:
                segments.append((start, i))
            continue
        else:
            if i > start + 2*buffer: # if not sufficiently long, keep moving on
                segments.append((start, i))
            start = i + 1

    # Calculate the mean of the loadcells in each segment and subtract it from the data up to the start of the next segment
    worst_cases = np.zeros(len(loadcell_cols))
    new_worst_cases = np.zeros(len(loadcell_cols))
    worst_case_times = np.zeros(len(loadcell_cols))
    for seg, (zero_start, zero_end) in enumerate(segments):
        mean = ldf.iloc[zero_start+buffer : zero_end-buffer,].mean()
        if (np.isnan(mean).any()):
            print(ldf.iloc[zero_start+buffer : zero_end-buffer].describe())

        new_worst_cases = np.fmax(worst_cases, np.abs(mean))
        for i in range(len(loadcell_cols)):
            if new_worst_cases[i] != worst_cases[i]:
                worst_case_times[i] = df['Time'].iloc[zero_start] - df['Time'].iloc[0]
        worst_cases = new_worst_cases

        if seg < len(segments) - 1:
            next_zero_start = segments[seg + 1][0]
        else:
            next_zero_start = len(ldf)

        ldf.iloc[zero_start:next_zero_start,] = ldf.iloc[zero_start:next_zero_start,] - mean

    print(f"Worst case hysteresis readings per loadcell: {worst_cases}")
    print(f"Worst-case times (ms): {worst_case_times}")
    df[loadcell_cols] = ldf

    return df

if __name__ == "__main__":
    import os
    import matplotlib.pyplot as plt
    from pathlib import Path
    from _io_utils import read_data
    from _transient_remover import remove_transients
    from _run_plotter import plot_data2

    # make a directory from the path string
    directory = options['pathname'] # directory where the files are located

    # read in the data
    data = read_data(directory)

    if options['daq_version'] == 0:
        channels = []
        first_loadcell_ind = data[0][1].columns.get_loc('Loadcell_' + str(options['channel_start']))
        for i in range(options['loadcell_count']):
            channels.append(first_loadcell_ind + i)
    
    for (name, df) in data:
        # zero the data
        df = zero(df, length=100, forces=True, loadcells=True)
        df = threshold_cop(df, threshold=50)
        df = remove_hysteresis(df, threshold=10)

        # plot the data
        plot_data2(df, channels)

    