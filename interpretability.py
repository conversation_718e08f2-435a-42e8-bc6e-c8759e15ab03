#!/usr/bin/env python
"""interpretability_suite.py – v0.5  (2025-06-24)

Full toolkit restored – **predict**, **shap**, **sensitivity**, **pdp**, and **rpdp** –
all with model-name-aware titles and, for *rpdp*, tighter layout + linked X-axes.

Main CLI flags (unchanged):
```
--synthetic  --synthetic-mode grid|random  --grid-points N  --range MIN MAX
--plotly     --output-index k             --feature-index j  --nsamples auto
--scaling auto|in_model|external
```
"""
from __future__ import annotations

import argparse, csv, sys, math
from pathlib import Path
from typing import List, Optional
import numpy as np, joblib, shap, onnxruntime as ort
from numpy.typing import NDArray
from matplotlib import pyplot as plt
try:
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
except ModuleNotFoundError:
    go = make_subplots = None  # Plotly optional

# ────────────────────────────────────────────────────── Util helpers

def _parse_out_names(tname: str) -> List[str]:
    return tname.split(",") if "," in tname else (tname.split("_") if "_" in tname else [tname])

def _title(txt: str, model_name: str) -> str: return f"{txt}  ({model_name})"

# ────────────────────────────────────────────────────── Model wrapper
class ForcePlateModel:
    def __init__(self, path: str | Path, scale_in_model: Optional[bool] = None):
        p = Path(path); self.model_name = p.stem
        self.sess = ort.InferenceSession(str(p), providers=["CPUExecutionProvider"])
        self.in_name = self.sess.get_inputs()[0].name
        self.out_name = self.sess.get_outputs()[0].name
        self.out_names = _parse_out_names(self.out_name)
        self.n_in = self.sess.get_inputs()[0].shape[1]
        self.n_out = self.sess.get_outputs()[0].shape[1]
        xscl, yscl = p.with_suffix(".xscl"), p.with_suffix(".yscl")
        self.scale_in_model = (not xscl.exists()) if scale_in_model is None else scale_in_model
        self.xscl = self.yscl = None
        if not self.scale_in_model:
            self.xscl, self.yscl = joblib.load(xscl), joblib.load(yscl)
    def _pre(self,x): return x if self.scale_in_model else self.xscl.transform(x)
    def _post(self,y): return y if self.scale_in_model else self.yscl.inverse_transform(y)
    def predict(self,x): return self._post(self.sess.run([self.out_name],{self.in_name:self._pre(x)})[0].astype(np.float32))

# ────────────────────────────────────────────────────── Data helpers

def synthetic(n_feats, mode, pts, lo, hi):
    if mode=="random": return np.random.uniform(lo,hi,(pts,n_feats)).astype(np.float32)
    grid=np.linspace(lo,hi,pts); return np.tile(grid[:,None],(1,n_feats)).astype(np.float32)

def load_matrix(path: Path):
    return (np.load(path) if path.suffix==".npy" else __import__('pandas').read_csv(path).values).astype(np.float32)

# ────────────────────────────────────────────────────── Plot wrappers

def _show_plotly(fig):
    if go is None: raise RuntimeError("plotly not installed – drop --plotly")
    fig.show()

def _save_png(fig, fname):
    fig.savefig(fname, dpi=200, bbox_inches="tight"); plt.close(fig)

# ────────────────────────────────────────────────────── SHAP

def compute_shap(model, X, nsamples):
    explainer=shap.KernelExplainer(model.predict, shap.sample(X,100))
    return [explainer.shap_values(X,nsamples=nsamples)[k] for k in range(model.n_out)]

def plot_shap(shap_list, feat_names, out_names, mdl, interactive):
    if interactive:
        fig=go.Figure();
        for k,vals in enumerate(shap_list):
            fig.add_trace(go.Bar(x=feat_names, y=np.mean(np.abs(vals),0), name=out_names[k]))
        fig.update_layout(title=_title("Mean |SHAP|",mdl)); _show_plotly(fig)
    else:
        for k,vals in enumerate(shap_list):
            plt.figure(figsize=(6,4)); shap.summary_plot(vals,feature_names=feat_names,show=False)
            plt.title(_title(f"SHAP – {out_names[k]}",mdl)); plt.savefig(f"shap_{out_names[k]}_{mdl}.png",dpi=200); plt.close()

# ────────────────────────────────────────────────────── Sensitivity (Jacobian)

def jacobian(model,X,eps=100):
    n=X.shape[0]; J=np.zeros((n,model.n_out,model.n_in),np.float32)
    for j in range(model.n_in):
        Xp,Xm=X.copy(),X.copy(); Xp[:,j]+=eps; Xm[:,j]-=eps
        J[:,:,j]=(model.predict(Xp)-model.predict(Xm))/(2*eps)
    return J

def plot_sensitivity(J, feat_names, out_names, mdl, interactive):
    M=np.mean(np.abs(J),0)
    if interactive:
        fig=go.Figure(data=go.Heatmap(z=M,x=feat_names,y=out_names));
        fig.update_layout(title=_title("Mean |∂output/∂input|",mdl)); _show_plotly(fig)
    else:
        import seaborn as sns; plt.figure(figsize=(6,4));
        sns.heatmap(M,annot=True,fmt=".2e",xticklabels=feat_names,yticklabels=out_names)
        plt.title(_title("Mean |∂output/∂input|",mdl)); plt.savefig(f"sensitivity_{mdl}.png",dpi=200); plt.close()

# ────────────────────────────────────────────────────── PDP (forward)

def pdp(model,X,fi,pts):
    base=X.mean(0); lo,hi=X[:,fi].min(),X[:,fi].max(); grid=np.linspace(lo,hi,pts)
    P=np.zeros((pts,model.n_out),np.float32)
    for i,g in enumerate(grid):
        Xmod=np.tile(base,(X.shape[0],1)); Xmod[:,fi]=g; P[i]=model.predict(Xmod).mean(0)
    return grid,P

def plot_pdp(grid,P,feat,out_names,mdl,interactive):
    if interactive:
        fig=go.Figure();
        for j,name in enumerate(out_names): fig.add_trace(go.Scatter(x=grid,y=P[:,j],mode="lines",name=name))
        fig.update_layout(title=_title(f"PDP – {feat}",mdl),xaxis_title=feat,yaxis_title="Partial dep."); _show_plotly(fig)
    else:
        plt.figure(figsize=(6,4));
        for j,name in enumerate(out_names): plt.plot(grid,P[:,j],label=name)
        plt.title(_title(f"PDP – {feat}",mdl)); plt.xlabel(feat); plt.legend(); _save_png(plt.gcf(),f"pdp_{feat}_{mdl}.png")

# ────────────────────────────────────────────────────── Reverse PDP

def rpdp_curves(model,X,out_idx,pts):
    base=X.mean(0); grids=[]; curves=np.zeros((model.n_in,pts),np.float32)
    for fi in range(model.n_in):
        lo,hi=X[:,fi].min(),X[:,fi].max(); g=np.linspace(lo,hi,pts); grids.append(g)
        Xm=np.tile(base,(pts,1)); Xm[:,fi]=g; curves[fi]=model.predict(Xm)[:,out_idx]
    return grids,curves

def _fit_origin(g,c):
    mask=g>0; gpos,cpos=g[mask],c[mask];
    if gpos.size==0: return math.nan,np.full_like(c,math.nan),math.nan
    m=(gpos@cpos)/(gpos@gpos); yhat=m*g; r2=1-np.sum((c-yhat)**2)/np.sum((c-c.mean())**2)
    return m,yhat,r2

def plot_rpdp(grids,curves,feat_names,out_name,mdl,interactive):
    n=len(feat_names)
    if interactive:
        if make_subplots is None: raise RuntimeError("plotly not installed")
        fig=make_subplots(rows=n,cols=1,shared_xaxes=True,vertical_spacing=0.02,subplot_titles=feat_names)
        for i,(g,c) in enumerate(zip(grids,curves)):
            m,yhat,r2=_fit_origin(g,c)
            fig.add_trace(go.Scatter(x=g,y=c,mode="lines",name=feat_names[i]),row=i+1,col=1)
            fig.add_trace(go.Scatter(x=g,y=yhat,mode="lines",line=dict(dash="dash"),name=f"m={m:.3g} R²={r2:.3f}"),row=i+1,col=1)
        fig.update_layout(height=320*n,title=_title(f"Reverse PDP – {out_name}",mdl));fig.update_xaxes(matches='x'); _show_plotly(fig)
    else:
        fig,axs=plt.subplots(n,1,sharex=True,figsize=(6,3*n),constrained_layout=True)
        for ax,g,c,name in zip(axs,grids,curves,feat_names):
            m,yhat,r2=_fit_origin(g,c); ax.plot(g,c); ax.plot(g,yhat,'k--',label=f"m={m:.3g} R²={r2:.3f}"); ax.set_title(name); ax.legend(fontsize='x-small')
        fig.suptitle(_title(f"Reverse PDP – {out_name}",mdl),y=0.995); fig.savefig(f"rpdp_{out_name}_{mdl}.png",dpi=200); plt.close(fig)

# ────────────────────────────────────────────────────── CLI dispatcher

def main(argv=None):
    argv=argv or sys.argv[1:]
    ap=argparse.ArgumentParser("Interpretability suite")
    ap.add_argument("cmd",choices=["predict","shap","sensitivity","pdp","rpdp"])
    ap.add_argument("--model",required=True)
    src=ap.add_mutually_exclusive_group(required=True); src.add_argument("--data"); src.add_argument("--synthetic",action="store_true")
    ap.add_argument("--synthetic-mode",default="grid",choices=["grid","random"])
    ap.add_argument("--grid-points",type=int,default=1000)
    ap.add_argument("--range",nargs=2,type=float,default=[-8_388_608,8_388_607])
    ap.add_argument("--feature-index",type=int,default=0)
    ap.add_argument("--output-index",type=int,default=0)
    ap.add_argument("--nsamples",default="auto")
    ap.add_argument("--plotly",action="store_true")
    ap.add_argument("--scaling",choices=["auto","in_model","external"],default="auto")
    args=ap.parse_args(argv)

    model=ForcePlateModel(args.model,None if args.scaling=="auto" else args.scaling=="in_model")
    # input matrix
    if args.synthetic:
        lo,hi=args.range; X=synthetic(model.n_in,args.synthetic_mode,args.grid_points,lo,hi)
    else:
        X=load_matrix(Path(args.data))
    if X.shape[1]!=model.n_in: raise ValueError("Column mismatch: file vs model")

    feat_names=[f"LC{i+1}" for i in range(model.n_in)]

    if args.cmd=="predict":
        P=model.predict(X)
        with open("predictions.csv","w",newline="") as f:
            w=csv.writer(f);w.writerow([f"out{i}" for i in range(model.n_out)]);w.writerows(P)

    elif args.cmd=="shap":
        S=compute_shap(model,X,args.nsamples); plot_shap(S,feat_names,model.out_names,model.model_name,args.plotly)

    elif args.cmd=="sensitivity":
        J=jacobian(model,X); plot_sensitivity(J,feat_names,model.out_names,model.model_name,args.plotly)

    elif args.cmd=="pdp":
        grid,P=pdp(model,X,args.feature_index,args.grid_points); plot_pdp(grid,P,feat_names[args.feature_index],model.out_names,model.model_name,args.plotly)

    elif args.cmd=="rpdp":
        grids,curves=rpdp_curves(model,X,args.output_index,args.grid_points)
        plot_rpdp(grids,curves,feat_names,model.out_names[args.output_index],model.model_name,args.plotly)
        print(f"Linearity for {model.out_names[args.output_index]} – {model.model_name}")
        for n,(g,c) in zip(feat_names,zip(grids,curves)):
            m,_,r2=_fit_origin(g,c); print(f"  {n:>5}: m={m: .3e}  R²={r2:5.3f}")

if __name__=='__main__':
    main()