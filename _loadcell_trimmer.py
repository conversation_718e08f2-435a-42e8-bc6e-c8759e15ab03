# Remove loadcells 1-6 or 7-12 as needed from rig captures involving 12-input plate DAQs. 
import os
import pandas as pd

def trim_loadcells_in_dir(directory, out_directory, remove):
    if not os.path.exists(out_directory):
        os.makedirs(out_directory)

    # loop through all csv files in the provided directory and its subdirectories
    # for each, read in the data, remove the loadcells, and save
    for root, dirs, files in os.walk(directory):
        for i,name in enumerate(files):
            if name.endswith('.csv'):
                print(f'Processing file {i+1} of {len(files)}: {name}')
                # read in the data
                data = pd.read_csv(os.path.join(root, name))
                data = trim_loadcells(data, remove)
                # save the data
                data.to_csv(os.path.join(out_directory, name), index=False)

def trim_loadcells(data, channels_to_trim):
    # remove the loadcells
    data = data.drop(channels_to_trim, axis=1)
    # find the index of the first data column starting with 'Loadcell_'
    loadcell_1_ind = data.columns.get_loc(data.columns[data.columns.str.startswith('Loadcell_')][0])
    last_loadcell_ind = data.columns.get_loc(data.columns[data.columns.str.startswith('Loadcell_')][-1])
    # rename the remaining columns labelled 'Loadcell_' so they start at Loadcell_1
    return data.rename(columns={col: f'Loadcell_{i+1}' for i,col in enumerate(data.columns[loadcell_1_ind:last_loadcell_ind+1])})
    
if __name__ == '__main__':
    keep_plate = 1 # 1 for plate 1, 2 for plate 2
    # make a directory from the path string
    directory = 'data\platev01\Plate V01 - 2022-12-07' # directory where the files are located
    overwrite = False # overwrite the original files or make a new folder

    if overwrite:
        out_directory = directory
    else:
        out_directory = directory + '_trimmed'

    if keep_plate == 1:
        remove = [7,8,9,10,11,12]
    else:
        remove = [1,2,3,4,5,6]

    remove = ['Loadcell_' + str(i) for i in remove]
    trim_loadcells_in_dir(directory, out_directory, remove)