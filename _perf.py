import numpy as np
import pandas as pd
from sklearn.metrics import r2_score, mean_absolute_percentage_error, mean_squared_error
import plotly.express as px
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.offline as py_offline

def score(pred, target):
    '''Calculates the average CoP distance error and force error - 
    this to keep a consistent metric of how well the model is performing regardless of 
    what loss function / criterion we use.'''
    # Mean distance error for centre of pressure
    cop_cols = [col for col in target.columns if 'C' in col]
    if len(cop_cols) > 0:
        return np.mean(np.abs(pred[cop_cols] - target[cop_cols]).to_numpy())

    # Mean absolute error for force
    force_cols = [col for col in target.columns if 'F' in col]
    if len(force_cols) > 0:
        return np.mean(np.abs(pred[force_cols] - target[force_cols]).to_numpy())
    
    return 0

def analyse_model_perf(Y_pred, Y_true, Y_CoPs, target_cols, plate_size=(500,500), plot=True, threshold=10):
    pred_df = pd.DataFrame(Y_pred, columns=target_cols)
    true_df = pd.DataFrame(Y_true, columns=target_cols)

    plt.scatter(Y_CoPs['Cx'], Y_CoPs['Cy'])

    datapoints = len(pred_df.iloc[:,0])
    force_cols = [col for col in target_cols if 'F' in col]
    if len(force_cols) > 0:
        force_score = score(pred_df.loc[:,force_cols], true_df.loc[:,force_cols])
        print(force_score)
        print(f"Average force error on the {datapoints} " +
            f"test datapoints: {force_score} N")

    cop_cols = [col for col in target_cols if 'C' in col]
    if len(cop_cols) > 0:
        cop_score = score(pred_df.loc[:,cop_cols], true_df.loc[:,cop_cols])
        print(f"Average CoP error on the {datapoints} " +
            f"test datapoints: {cop_score} mm")
            
    for col in target_cols:
        analyse_target_perf(pred_df.loc[:,col], true_df.loc[:,col], col, Y_CoPs, plate_size, plot)

def analyse_target_perf(predictions, targets, name, Y_CoPs, plate_size=(500,500), plot=False):
    '''Single column performance analysis'''
    plate_width_x = plate_size[0]
    plate_width_y = plate_size[1]
    unit = ''
    if name in ['Fx', 'Fy', 'Fz']:
        unit = 'N'
    elif name in ['Cx', 'Cy']:
        unit = 'mm'

    print(f"Performance analysis for {name}")
    print("")
    errors = np.abs(predictions - targets)
    ave_error = np.mean(errors)
    min_error = np.min(errors)
    max_error = np.max(errors)
    sdev_error = np.std(errors)
    q1_error = np.quantile(errors, 0.25)
    q3_error = np.quantile(errors, 0.75)
    median_error = np.median(errors)
    ci95_error = 1.96 * sdev_error
    mape = mean_absolute_percentage_error(targets, predictions) * 100
    rmse = np.sqrt(mean_squared_error(targets, predictions))
    r2 = r2_score(targets, predictions)
    
    maxval = np.max(np.abs(targets))
    minval = np.min(np.abs(targets))
    if name == 'Cx': fs = plate_width_x
    elif name == 'Cy': fs = plate_width_y
    else: fs = maxval - minval
    ave_error_percent_fullscale = (ave_error / fs) * 100
    min_error_percent_fullscale = (min_error / fs) * 100
    max_error_percent_fullscale = (max_error / fs) * 100
    sdev_error_percent_fullscale = (sdev_error / fs) * 100
    q1_error_percent_fullscale = (q1_error / fs) * 100
    q3_error_percent_fullscale = (q3_error / fs) * 100
    median_error_percent_fullscale = (median_error / fs) * 100
    ci95_error_percent_fullscale = (ci95_error / fs) * 100
    
    print(f"Max {name} / full-scale: {fs:.2f} {unit}")
    print(f"Average {name} error (95% CI): {ave_error:.2f} ({ci95_error:.2f}) {unit}")
    print(f"Average {name} error (95% CI) as % of full-scale: {ave_error_percent_fullscale:.2f} ({ci95_error_percent_fullscale:.2f})%")
    print(f"Standard deviation of {name} error: {sdev_error:.2f} {unit}")
    print(f"Standard deviation of {name} error as % of full-scale: {sdev_error_percent_fullscale:.2f}%")
    print(f"RMSE of {name}: {rmse:.2f} {unit}")
    print(f"RMSE of {name} as % of full-scale: {(rmse/fs)*100:.2f}%")
    print(f"MAPE {name}: {mape:.2f}%")
    print(f"R2 score of {name}: {r2:.5f}")
    print(f"{name} error quantiles in {unit}: {min_error:.2f}, {q1_error:.2f}, {median_error:.2f}, {q3_error:.2f}, {max_error:.2f}")
    print(f"{name} error quantiles as % of full-scale: {min_error_percent_fullscale:.2f}, {q1_error_percent_fullscale:.2f}, {median_error_percent_fullscale:.2f}, {q3_error_percent_fullscale:.2f}, {max_error_percent_fullscale:.2f}")
    print("")

    error_percents_fs = (np.abs(targets - predictions) / fs) * 100
    error_percents_load = np.abs((targets - predictions) / targets) * 100

    if plot:
        # Plot an error heatmap to see if error is related to CoP
        fig = px.density_heatmap(data_frame=Y_CoPs, x='Cx', y='Cy', z=error_percents_fs, histfunc="avg", 
            width=(400*(plate_width_x/500))+100, height=400, 
            range_x=(-plate_width_x/2,plate_width_x/2), range_y=(-plate_width_y/2,plate_width_y/2),
            labels={
                    'Cx': f"X Centre of Pressure ({unit})",
                    'Cy': f"Y Centre of Pressure ({unit})",
                    'z': "Error",
                    }, 
            title=f"Avg {name} Error (% of FS) vs CoP")
        
        fig.update_traces(xbins=dict(start=-plate_width_x/2, end=plate_width_x/2, size=15), selector=dict(type='histogram2d'))
        fig.update_traces(ybins=dict(start=-plate_width_y/2, end=plate_width_y/2, size=15), selector=dict(type='histogram2d'))
        fig.update_layout(coloraxis_cmin=0, coloraxis_cmax=2)
        # fig.show()
        py_offline.plot(fig)

# GET MEDIANS VS COP
        # # Assuming error_percents_fs is already added to Y_CoPs DataFrame
        # # Assuming error_percents_fs is a list or array. Let's add it to Y_CoPs DataFrame if it's not already included
        # Y_CoPs['error_percents_fs'] = error_percents_fs

        # # Define the bins for 'Cx' and 'Cy'
        # x_bins = np.arange(-plate_width_x/2, plate_width_x/2 + 15, 15)
        # y_bins = np.arange(-plate_width_y/2, plate_width_y/2 + 15, 15)

        # # Calculate midpoints of the bins for plotting
        # x_bin_mids = (x_bins[:-1] + x_bins[1:]) / 2
        # y_bin_mids = (y_bins[:-1] + y_bins[1:]) / 2

        # # Use cut to assign each 'Cx' and 'Cy' value to a bin
        # Y_CoPs['x_bin'] = pd.cut(Y_CoPs['Cx'], bins=x_bins, labels=np.arange(len(x_bins)-1))
        # Y_CoPs['y_bin'] = pd.cut(Y_CoPs['Cy'], bins=y_bins, labels=np.arange(len(y_bins)-1))

        # # Group by the bin columns and calculate the median of 'error_percents_fs' within each group
        # grouped = Y_CoPs.groupby(['x_bin', 'y_bin'])['error_percents_fs'].median().reset_index()

        # # Pivot the grouped DataFrame to create a 2D array suitable for a heatmap
        # pivot_table = grouped.pivot(index='y_bin', columns='x_bin', values='error_percents_fs').fillna(0)

        # # Create the heatmap
        # fig = go.Figure(data=go.Heatmap(
        #     z=pivot_table.values,
        #     x=x_bin_mids,  # Use the calculated midpoints for the x-axis
        #     y=y_bin_mids,  # Use the calculated midpoints for the y-axis
        #     colorbar=dict(title='Median Error'),
        #     coloraxis="coloraxis"
        # ))

        # # Update the layout
        # fig.update_layout(
        #     title=f"Median {name} Error (% of FS) vs CoP",
        #     xaxis_title=f"X Centre of Pressure ({unit})",
        #     yaxis_title=f"Y Centre of Pressure ({unit})",
        #     width=(400*(plate_width_x/500))+150,
        #     height=400,
        #     coloraxis_colorbar=dict(title="Median Error (%)"),
        #     coloraxis_cmin=0, 
        #     coloraxis_cmax=1
        # )

        # fig.show()

        # # plt.plot(targets)#[0:10000])
        # # plt.plot(predictions)#[0:10000])
        # # plt.legend(['Actual', 'Predicted'])#, 'Predicted + std', 'Predicted - std'])
        # # plt.show()

        # df = pd.DataFrame({'Actual': targets, 'Predicted': predictions})
        # fig = px.line(df)
        # fig.show()

        # fig = px.scatter(x=targets, y=predictions,
        #     labels={
        #             'x': f"Actual {name} ({unit})",
        #             'y': f"Predicted {name} ({unit})",
        #             },
        #     title=f"Actual vs Predicted {name}")
        # fig.update_xaxes(scaleratio=1)
        # fig.show()

        # # from scipy.stats import binned_statistic
        # # ret = binned_statistic(targets, error_percents_fs, bins=100)
        # # plt.stairs(ret.statistic, ret.bin_edges)
        # # plt.title(f"Average {name} Error (% of FS) vs True Value")
        # # plt.xlabel(f"True {name} ({unit})")
        # # plt.ylabel(f"Avg {name} Error (% of FS)")
        # # plt.show()

        # # do the above with plotly
        # fig = px.histogram(x=targets, y=error_percents_fs, histfunc="avg",
        #     width=800, height=600, 
        #     labels={
        #             'x': f"True {name} ({unit})",
        #             'y': f"Avg {name} Error (% of FS)",
        #             }, 
        #     title=f"Avg {name} Error (% of FS) vs True Value")
        # fig.show()

        # fig = px.histogram(x=targets, y=error_percents_load, histfunc="avg",
        #     width=800, height=600, 
        #     labels={
        #             'x': f"True {name} ({unit})",
        #             'y': f"Avg {name} Error (% of Load)",
        #             }, 
        #     title=f"Avg {name} Error (% of Load) vs True Value")
        # fig.show()

    # px.scatter(data_frame=Y_CoPs, x='Cx', y='Cy', color=Fx_errors, width=600, height=600, 
    #    range_x=(-225,225), range_y=(-225,225),
    # #    range_y=(100,500), range_x=(400,800),
    #    labels={
    #         'Cx': "X Centre of Pressure (mm)",
    #         'Cy': "Y Centre of Pressure (mm)",
    #         'color': "Error (N)",
    #         }, 
    #    title="X-Direction Force Error vs Centre of Pressure").show()

    # px.scatter_3d(data_frame=Y_CoPs, x='Cx', y='Cy', z=Fx_errors, color=Fx_errors, width=800, height=800, 
    #    range_x=(-225,225), range_y=(-225,225),
    # #    range_y=(100,500), range_x=(400,800),
    #    labels={
    #         'Cx': "X Centre of Pressure (mm)",
    #         'Cy': "Y Centre of Pressure (mm)",
    #         'z': "Error (N)",
    #         'color': "Error (N)",
    #         }, 
    #    title="X-Direction Force Error vs Centre of Pressure").show()
    

# def make_performance_analysis(Y_true, Y_pred, Y_CoPs, plot=True):
#     # make a performance analysis of the model
#     # Y_true and Y_pred are the true and predicted outputs
#     # Y_CoPs are the CoPs of the test data
#     # plot is whether to plot the results

#     # make a dataframe to store the results
#     results = pd.DataFrame(columns=['Force', 'Average Error (N)', '95% CI', 'Average Error (%)', 'Standard Deviation (N)', 'Standard Deviation (%)', 'MAPE (%)', 'R2 Score'])

#     # make a list of the forces
#     forces = ['Fx', 'Fy', 'Fz', 'Cx', 'Cy']

#     # loop through the forces
#     for i, force in enumerate(forces):
#         # get the true and predicted values for the force
#         Y_true_force = Y_true[:,i]
#         Y_pred_force = Y_pred[:,i]

#         # get the average error
#         ave_error = np.mean(np.abs(Y_true_force - Y_pred_force))
#         # get the 95% confidence interval
#         sdev = np.std(np.abs(Y_true_force - Y_pred_force))
#         ci95 = 1.96 * sdev
#         # get the average error as a percentage of the full-scale force
#         max_force = np.max(np.abs(Y_true_force))
#         min_force = np.min(np.abs(Y_true_force))
#         full_scale = max_force - min_force
#         ave_error_percent_fullscale = (ave_error / full_scale) * 100
#         # get the standard deviation of the error
#         sdev_error = np.std(np.abs(Y_true_force - Y_pred_force))
#         # get the standard deviation of the error as a percentage of the full-scale force
#         sdev_error_percent_fullscale = (sdev_error / full_scale) * 100
#         # get the MAPE
#         mape = np.mean(np.abs((Y_pred_force - Y_true_force) / Y_true_force)) * 100
#         # get the R2 score
#         r2_score = r2_score(Y_true_force, Y_pred_force)

#         # add the results to the dataframe
#         results.loc[i] = [force, ave_error, ci95, ave_error_percent_fullscale, sdev_error, sdev_error_percent_fullscale, mape, r2