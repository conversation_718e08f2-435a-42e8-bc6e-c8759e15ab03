import onnx
import onnxruntime as ort

# Load the ONNX model
onnx_model = onnx.load('models/vader03.4/smart-waterfall-207.onnx')
onnx.checker.check_model(onnx_model)

# Print a human-readable representation of the model graph
print(onnx.helper.printable_graph(onnx_model.graph))

# Create an ONNX Runtime session
ort_session = ort.InferenceSession('models/vader03.4/smart-waterfall-207.onnx')

# Prepare dummy input for inference
input_name = ort_session.get_inputs()[0].name
output_name = ort_session.get_outputs()[0].name
dummy_input = [[1,1,1,1,1,1]]

# Run inference
output = ort_session.run([output_name], {input_name: dummy_input})
print("ONNX Runtime Output:", output)