# plotly animation - looks nice for video overlay but has to be manually saved
# makes a line plot of forces and scatter of CoP over time
import pandas as pd
import numpy as np
import math
import plotly.graph_objects as go
import plotly.offline as py_offline
from plotly.subplots import make_subplots
from _io_utils import read_inferred_data, preprocess_data
from tqdm import tqdm

# Define color constants to avoid magic numbers
COLORS = {
    'BACKGROUND': '#F6F5F1',        # White background
    'PAPER': '#F6F5F1',             # White paper background
    'DARK_GREEN': '#004225',        # Dark green for lines and text
    'LIME_GREEN': '#86C900',        # Bright lime green for highlights
    'GRAY': 'gray',                 # Gray for zero lines
    'LIGHT_GRAY': 'silver',         # Light gray for plate outline
    'TRANSPARENT': 'rgba(0, 0, 0, 0)',  # Transparent
    'SEMI_TRANSPARENT_WHITE': 'rgba(255, 255, 255, 0.5)',  # Semi-transparent white
}

options = {
    'pathnames': ['outputs/plateo06.3 CT Tests/Acinotech Forceplate 5 Lab Run Heel 2 R->L 20250424_152226.csv'],
    # 'pathnames': ['outputs/plateo06.3 CT Tests/Acinotech Forceplate 5 ARU 30cm hop 2 20250424_173500.csv'],
    # 'pathnames': ['outputs/plateo06.3 CT Tests/Acinotech Forceplate 5 ARU 30cm hop 4 20250424_175139.csv'],
    'plates': ['Acinotech Forceplate 5'],
    'analysis_threshold': 100, # Fz threshold to be included in accuracy analysis
    'daq_version': 1, # ["STM32", "Pi", 0, 1] 0 for ESP32, 1 for EtherCAT
    'save': False, # save to outputs folder
    'plate_sizes': [(500,500)], # x,y
    'plate_centers': [(0, 0)], # x,y, relative to ground truth COP origin
    'camera_viewpoint': '39',
    'rotate_force_axes': [-1,-1,1], # swap axes to be reaction-oriented (force applied to body) rather than action-oriented (force applied to plate)
    'frame_lims': (6000,6450), # 39 None or start and end frames of the portion to animate
    # 'frame_lims': (12500,18000), # 163
    # 'frame_lims': (5000, 25000), # 164
    'frame_spacing': 1,
    'show_axis_ticks': False,
    'show_title': False,
    'force_plot_only': False, # Whether to show only the force plot without the CoP plot
    'window_size': None, # Number of frames to show in the sliding window (None for all frames)
    'filter': {
        'enabled': True,  # Whether to apply filtering to the data
        'window_length': 11,  # Window length for Savitzky-Golay filter (must be odd)
        'polyorder': 3,  # Polynomial order for Savitzky-Golay filter
    },
}

if not options['daq_version'] in ["STM32", "Pi", 0, 1]:
    raise ValueError("Invalid DAQ version specified - must be one of ['STM32', 'Pi', 0, 1]")

def plot_2d_animated(
    df,
    force_cols=['Fx', 'Fy', 'Fz'],
    cop_cols=['Cx', 'Cy'],
    title=None,
    force_colors=None,
    plate_size=(500, 500),
    force_threshold=50,
    show_axis_ticks=True,
    show_title=True,
    create_tooltips=False,
    force_plot_only=False,
    window_size=None
):
    """
    Create an animated 2D plot of force data and center of pressure.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame containing the preprocessed force and COP data
    force_cols : list
        List of column names for force components (e.g., ['Fx', 'Fy', 'Fz'])
    cop_cols : list
        List of column names for center of pressure (e.g., ['Cx', 'Cy'])
        The first element is used for the y-axis, the second for the x-axis in the COP plot
    title : str, optional
        Title for the plot
    force_colors : dict, optional
        Dictionary mapping force column names to colors
    plate_size : tuple, optional
        Size of the force plate (width, length) in mm
    force_threshold : float, optional
        Threshold for the vertical force to include in COP trajectory
    show_axis_ticks : bool, optional
        Whether to show axis ticks and labels
    show_title : bool, optional
        Whether to show the plot title
    create_tooltips : bool, optional
        Whether to create tooltips for data points
    force_plot_only : bool, optional
        Whether to show only the force plot without the CoP plot
    window_size : int, optional
        Number of frames to show in the sliding window. If None, all frames are shown.
        This creates a "live" view where only the most recent frames are displayed.

    Returns:
    --------
    plotly.graph_objects.Figure
        The plotly figure object
    """
    # Get time values for animation frames
    time_values = df.index

    # Set default colors if not provided
    if force_colors is None:
        force_colors = {
            force_cols[0]: COLORS['DARK_GREEN'],
            force_cols[1]: COLORS['LIGHT_GRAY'],
            force_cols[2]: COLORS['LIME_GREEN']
        }

    # Define descriptive names for force axes
    force_descriptions = [
        'Anterior-Posterior',
        'Medial-Lateral',
        'Vertical']

    # Create a figure with one or two subplots based on force_plot_only flag
    if force_plot_only:
        fig = make_subplots(
            rows=1, cols=1,
            subplot_titles=["Ground Reaction Force"],
            vertical_spacing=0.14
        )
    else:
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=["Ground Reaction Force", "Center of Pressure"],
            vertical_spacing=0.14
        )

    # Add traces for each force component with custom hover information
    if create_tooltips:
        tooltip_text = df.apply(
            lambda row: '<br>'.join(
                [f"{col}: {row[col]:.2f}" for col in force_cols] +
                [f"{cop_cols[0]}: {row[cop_cols[0]]:.2f}", f"{cop_cols[1]}: {row[cop_cols[1]]:.2f}"]
            ),
            axis=1
        )
        hoverinfo = "text"
    else:
        tooltip_text = None
        hoverinfo = "skip"

    line_ids = []
    for f_index, force in enumerate(force_cols):
        line_ids.append(len(fig.data))
        fig.add_trace(
            go.Scatter(
                x=[], y=[],
                mode="lines",
                line=dict(color=force_colors[force]),
                name=force_descriptions[f_index],
                hoverinfo=hoverinfo,
                text=tooltip_text,
                showlegend=True
            ),
            row=1, col=1
        )

    # Add traces for the current data point in the force components
    marker_ids = []
    for force in force_cols:
        marker_ids.append(len(fig.data))
        fig.add_trace(
            go.Scatter(
                mode="markers",
                marker=dict(color=force_colors[force], size=10),
                showlegend=False
            ),
            row=1, col=1
        )

    # Initialize CoP trace IDs
    cop_line_id = None
    cop_dot_id = None

    # Only add CoP traces if not force_plot_only
    if not force_plot_only:
        # Add the plate outline as a shape
        half_width = plate_size[0] / 2
        half_height = plate_size[1] / 2
        fig.update_layout(
            shapes=[
                dict(
                    type="rect",
                    x0=-half_width, y0=-half_height,
                    x1=half_width, y1=half_height,
                    fillcolor=COLORS['LIGHT_GRAY'],
                    line=dict(width=0),
                    layer='below',
                    xref='x2', yref='y2'
                )
            ]
        )

        # Add an empty CoP trajectory trace
        cop_line_id = len(fig.data)
        fig.add_trace(
            go.Scatter(mode="lines", line=dict(color=COLORS['DARK_GREEN']), showlegend=False),
            row=2, col=1
        )

        # Add trace for the current Center of Pressure data point
        cop_dot_id = len(fig.data)
        fig.add_trace(
            go.Scatter(mode="markers", marker=dict(color=COLORS['LIME_GREEN'], size=10),
                    showlegend=False),
            row=2, col=1
        )

    # Now, total traces are:
    # 0,1,2: Force component lines
    # 3,4,5: Current data points in force components
    # 6: CoP trajectory (empty)
    # 7: Current CoP point

    frames = []
    for t in tqdm(time_values):
        # Implement sliding window if window_size is specified
        if window_size is not None and window_size > 0:
            # Calculate the start of the window
            window_start = max(0, t - window_size)
            # Get data within the window
            window_data = df.loc[window_start:t]
            # For CoP trajectory, we still need to filter by force threshold
            valid_data = window_data[window_data[force_cols[2]] > force_threshold]

            # Calculate x-axis range for the sliding window effect
            x_min = window_start
            x_max = t + max(1, window_size * 0.05)  # Add a small buffer on the right
        else:
            # Use all data up to the current point if no window_size is specified
            window_data = df.loc[:t]
            valid_data = window_data[window_data[force_cols[2]] > force_threshold]

            # For non-windowed mode, use a static domain that expands rightwards
            # Start with a fixed minimum and expand the maximum as needed
            x_min = -50  # Fixed minimum
            x_max = max(time_values[-1], t + 50)  # Expand to at least the end of the data

        # Prepare frame data and trace indices based on force_plot_only flag
        frame_data = [
            #  1. growing line for every force (windowed if specified)
            *[
                go.Scatter(
                    x=window_data.index,
                    y=window_data[f],
                    mode="lines",
                    line=dict(color=force_colors[f])
                )
                for f in force_cols
            ],
            #  2. current-value markers
            *[go.Scatter(x=[t], y=[df.loc[t, f]]) for f in force_cols]
        ]

        trace_indices = line_ids + marker_ids

        # Add CoP data if not force_plot_only
        if not force_plot_only and cop_line_id is not None and cop_dot_id is not None:
            # Add CoP trajectory and current point
            frame_data.extend([
                #  3. CoP trajectory so far (optionally thinned and windowed)
                go.Scatter(x=valid_data[cop_cols[0]],
                        y=valid_data[cop_cols[1]],
                        line=dict(simplify=True)),
                #  4. current CoP point
                go.Scatter(x=[df.loc[t, cop_cols[0]]],
                        y=[df.loc[t, cop_cols[1]]])
            ])
            trace_indices.extend([cop_line_id, cop_dot_id])

        # Only create layout updates for sliding window mode
        if window_size is not None and window_size > 0:
            # Create a layout update for the x-axis range to implement the sliding window effect
            layout_update = {
                'xaxis.range': [x_min, x_max]
            }
            frames.append(
                go.Frame(
                    name=str(t),
                    data=frame_data,
                    traces=trace_indices,
                    layout=layout_update  # Add layout update to each frame
                )
            )
        else:
            # For non-windowed mode, don't update the layout in each frame
            # This allows the plot to maintain a static domain and expand rightwards
            frames.append(
                go.Frame(
                    name=str(t),
                    data=frame_data,
                    traces=trace_indices
                )
            )

    fig.frames = frames

    # Set the title
    if title is None:
        title = "Force and Center of Pressure Animation"

    # Align subplot titles to the left
    for i, annotation in enumerate(fig['layout']['annotations']):
        annotation['x'] = -0.1
        annotation['y'] = 1.58 - 0.55 * (i+1)
        annotation['xanchor'] = 'left'
        annotation['yanchor'] = 'bottom'
        annotation['font'] = dict(family="Montserrat, sans-serif", size=24, color=COLORS['DARK_GREEN'])

    # Update layout with title and axis labels
    fig.update_layout(
        title=title if show_title else None,
        height=900,  # Adjust height based on number of plots
        width=700 if force_plot_only else 600,   # Set a fixed width to match the height
        margin=dict(t=100, b=100),
        plot_bgcolor=COLORS['BACKGROUND'],
        paper_bgcolor=COLORS['PAPER'],
        legend=dict(
            x=1.15,
            y=0.99,
            xanchor='right',
            yanchor='top',
            bgcolor=COLORS['TRANSPARENT'],  # Semi-transparent background
            bordercolor=COLORS['TRANSPARENT'],
            borderwidth=1,
            font=dict(family="Montserrat, sans-serif", size=18)
        ),
        hovermode="closest",
        font=dict(family="Montserrat, sans-serif"),  # Set Montserrat as the default font for the entire plot
        updatemenus=[
            {
                'type': 'buttons',
                'buttons': [
                    {
                        'label': 'Play',
                        'method': 'animate',
                        'args': [
                            None,
                            {
                                'frame': {'duration': 10, 'redraw': False},
                                'fromcurrent': True,
                                'transition': {'duration': 0}
                            }
                        ]
                    },
                    {
                        'label': 'Pause',
                        'method': 'animate',
                        'args': [
                            [None],
                            {
                                'frame': {'duration': 0},
                                'mode': 'immediate'
                            }
                        ]
                    }
                ],
                'showactive': False,
                'x': 0.1,
                'y': -0.1,
                'xanchor': 'right',
                'yanchor': 'top'
            }
        ],
        sliders=[
            {
                'steps': [
                    {
                        'method': 'animate',
                        'args': [
                            [str(t)],
                            {
                                'mode': 'immediate',
                                'frame': {'duration': 0},
                                'transition': {'duration': 0}
                            }
                        ],
                        'label': str(t)
                    } for t in time_values[::max(1, len(time_values) // 100)]  # Use fewer steps for better performance
                ],
                'transition': {'duration': 0},
                'x': 0.1,
                'y': -0.1,
                'currentvalue': {
                    'font': {'size': 12, 'family': 'Montserrat, sans-serif'},
                    'prefix': 'Frame: ',
                    'visible': True,
                    'xanchor': 'right'
                },
                'len': 0.9
            }
        ]
    )

    # Calculate appropriate axis ranges
    force_min = df[force_cols].min().min() - 70
    force_max = df[force_cols].max().max() + 70

    # Update axes ranges and labels
    fig.update_yaxes(
        range=[force_min, force_max],
        title_text="Newtons",# if show_axis_ticks else "",
        # showticklabels=show_axis_ticks,
        showgrid=False,  # Remove grid lines
        zeroline=True,   # Show zero line
        zerolinecolor=COLORS['GRAY'],  # Make zero line gray
        zerolinewidth=1,  # Set zero line width
        row=1, col=1
    )
    # Set initial x-axis range based on window_size
    if window_size is not None and window_size > 0:
        # Start with a window-sized view for sliding window mode
        x_min = max(0, time_values[-1] - window_size)
        x_max = time_values[-1] + max(1, window_size * 0.05)
    else:
        # For non-windowed mode, use a static domain that shows the full data range
        x_min = -50  # Fixed minimum
        x_max = time_values[-1] + 50  # Fixed padding at the end

    fig.update_xaxes(
        range=[x_min, x_max],
        title_text="Frame" if show_axis_ticks else "",
        showticklabels=show_axis_ticks,
        showgrid=False,  # Remove grid lines
        zeroline=True,   # Show zero line
        zerolinecolor=COLORS['GRAY'],  # Make zero line gray
        zerolinewidth=1,  # Set zero line width
        row=1, col=1
    )

    # Only set up CoP axes if not force_plot_only
    if not force_plot_only:
        # Calculate appropriate COP ranges based on data and plate size
        half_width = plate_size[0] / 2
        half_height = plate_size[1] / 2
        cop_x_min = -half_width * 1.1
        cop_x_max = half_width * 1.1
        cop_y_min = -half_height * 1.1
        cop_y_max = half_height * 1.1

        # Set 1:1 aspect ratio for the COP plot (second subplot)
        fig.update_layout(
            yaxis2=dict(
                scaleanchor="x2",
                scaleratio=1,
            )
        )

        # Round to nearest 100mm for nice tick values
        x_min_tick = math.floor(cop_x_min / 100) * 100
        x_max_tick = math.ceil(cop_x_max / 100) * 100
        y_min_tick = math.floor(cop_y_min / 100) * 100
        y_max_tick = math.ceil(cop_y_max / 100) * 100

        # Create tick values every 100mm
        x_ticks = list(range(x_min_tick, x_max_tick + 100, 100))
        y_ticks = list(range(y_min_tick, y_max_tick + 100, 100))

        fig.update_xaxes(
            range=[cop_x_min, cop_x_max],
            title_text=f"{cop_cols[0]} (mm)" if show_axis_ticks else "",
            showticklabels=show_axis_ticks,
            tickmode='array',
            tickvals=x_ticks,
            ticktext=[str(x) for x in x_ticks],
            showgrid=True,
            gridcolor=COLORS['PAPER'],
            gridwidth=1,
            zeroline=True,
            zerolinecolor=COLORS['GRAY'],  # Match force plot zero line color
            zerolinewidth=1,       # Match force plot zero line width
            row=2, col=1
        )
        fig.update_yaxes(
            range=[cop_y_min, cop_y_max],
            title_text=f"{cop_cols[1]} (mm)" if show_axis_ticks else "",
            showticklabels=show_axis_ticks,
            tickmode='array',
            tickvals=y_ticks,
            ticktext=[str(y) for y in y_ticks],
            showgrid=True,
            gridcolor=COLORS['PAPER'],
            gridwidth=1,
            zeroline=True,
            zerolinecolor=COLORS['GRAY'],  # Match force plot zero line color
            zerolinewidth=1,       # Match force plot zero line width
            row=2, col=1
        )

    fig.show(renderer="browser", post_script=[f'document.body.style.background="{COLORS["BACKGROUND"]}";'])
    return fig

if __name__ == '__main__':
    # columns_to_use = ['Time', 'Fx_pred', 'Fy_pred', 'Fz_pred', 'Cx_pred', 'Cy_pred']
    columns_to_use = ['Time', 'Fx', 'Fy', 'Fz', 'Cx', 'Cy']
    output_names = ['Time', 'Fx', 'Fy', 'Fz', 'Cx', 'Cy']
    forces = ['Fx', 'Fy', 'Fz']
    CoPs = ['Cx', 'Cy']

    # Load the real data
    data = read_inferred_data(options['pathnames'], options['plates'], skip_rows=0, usecols=columns_to_use, names=output_names, rotation=options['rotate_force_axes'])

    # Process each trial separately
    for i, (trialname, plates) in enumerate(data):
        # Plot each plate separately (likely want to unify in future)
        for idx, (plate, df) in enumerate(plates):
            # Step 1: Preprocess the data
            processed_df, filter_info = preprocess_data(
                df=df,
                force_cols=forces,
                cop_cols=CoPs,
                frame_lims=options['frame_lims'],
                frame_spacing=options['frame_spacing'],

                filter_options=options['filter']
            )

            # Step 2: Create title with filter information if filtering was applied
            title = f"{trialname} - {plate}"
            if filter_info.get('applied', False):
                window_length = filter_info['window_length']
                polyorder = filter_info['polyorder']
                title = f"{title} (Filtered: SG w={window_length}, p={polyorder})"

            # Step 3: Plot the preprocessed data
            plot_2d_animated(
                df=processed_df,
                force_cols=forces,
                cop_cols=CoPs,
                title=title,
                force_colors=None,
                plate_size=options['plate_sizes'][idx] if idx < len(options['plate_sizes']) else (500, 500),
                show_axis_ticks=options['show_axis_ticks'],
                show_title=options['show_title'],
                create_tooltips=False,
                force_plot_only=options['force_plot_only'],
                force_threshold=options['analysis_threshold'],
                window_size=options['window_size']
            )